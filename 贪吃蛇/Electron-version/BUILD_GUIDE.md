# 贪吃蛇游戏 - 打包成DMG指导

本指导将详细说明如何将Electron版本的贪吃蛇游戏打包成macOS的DMG安装包。

## 前置要求

### 1. 系统要求
- macOS 10.14 或更高版本
- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器

### 2. 安装必要工具

#### 安装ImageMagick（用于生成图标）
```bash
# 使用Homebrew安装
brew install imagemagick

# 验证安装
convert --version
```

#### 安装Xcode Command Line Tools（如果还没安装）
```bash
xcode-select --install
```

## 步骤一：准备项目

### 1. 确保依赖已安装
```bash
cd Electron-version
npm install
```

### 2. 生成应用图标
```bash
cd assets
./create-icons.sh
```

如果脚本执行失败，手动生成图标：
```bash
# 生成PNG图标
convert icon.svg -resize 512x512 icon.png

# 生成ICNS图标（macOS专用）
mkdir icon.iconset
convert icon.svg -resize 16x16 icon.iconset/icon_16x16.png
convert icon.svg -resize 32x32 icon.iconset/<EMAIL>
convert icon.svg -resize 32x32 icon.iconset/icon_32x32.png
convert icon.svg -resize 64x64 icon.iconset/<EMAIL>
convert icon.svg -resize 128x128 icon.iconset/icon_128x128.png
convert icon.svg -resize 256x256 icon.iconset/<EMAIL>
convert icon.svg -resize 256x256 icon.iconset/icon_256x256.png
convert icon.svg -resize 512x512 icon.iconset/<EMAIL>
convert icon.svg -resize 512x512 icon.iconset/icon_512x512.png
convert icon.svg -resize 1024x1024 icon.iconset/<EMAIL>

iconutil -c icns icon.iconset
rm -rf icon.iconset
```

## 步骤二：测试应用

### 1. 开发模式测试
```bash
npm run dev
```

### 2. 生产模式测试
```bash
npm start
```

确保游戏功能正常：
- 游戏能正常启动
- 蛇能正常移动
- 食物能正常生成和被吃掉
- 分数能正常计算
- 设置面板能正常打开
- 音效正常播放（如果有）
- 菜单快捷键正常工作

## 步骤三：打包应用

### 1. 打包成应用程序
```bash
npm run pack
```
这会在 `dist/mac` 目录下生成 `.app` 文件。

### 2. 打包成DMG安装包
```bash
npm run build:dmg
```

或者使用完整的构建命令：
```bash
npm run build
```

### 3. 检查打包结果
打包完成后，在 `dist` 目录下会生成：
- `贪吃蛇游戏-1.0.0.dmg` - DMG安装包
- `贪吃蛇游戏-1.0.0-mac.zip` - ZIP压缩包
- `mac/` 目录 - 包含 `.app` 文件

## 步骤四：测试DMG安装包

### 1. 挂载DMG文件
双击生成的 `.dmg` 文件，系统会自动挂载。

### 2. 测试安装过程
- 将应用拖拽到 Applications 文件夹
- 从 Applications 文件夹启动应用
- 确认应用能正常运行

### 3. 测试卸载过程
- 从 Applications 文件夹删除应用
- 确认没有残留文件

## 故障排除

### 常见问题及解决方案

#### 1. 图标不显示
**问题**: 打包后应用图标显示为默认图标
**解决方案**:
- 确保 `assets/icon.icns` 文件存在
- 检查 `package.json` 中的图标路径配置
- 重新生成图标文件

#### 2. 应用无法启动
**问题**: 双击应用后没有反应或闪退
**解决方案**:
- 检查控制台日志：打开 Console.app 查看错误信息
- 确保所有依赖文件都已正确打包
- 检查 `main.js` 中的文件路径

#### 3. 代码签名问题
**问题**: macOS提示"无法验证开发者"
**解决方案**:
- 右键点击应用，选择"打开"
- 或在系统偏好设置 > 安全性与隐私中允许运行

#### 4. 打包失败
**问题**: electron-builder 报错
**解决方案**:
```bash
# 清理缓存
npm run clean
rm -rf node_modules
npm install

# 重新打包
npm run build:dmg
```

#### 5. DMG背景图片问题
**问题**: DMG打开后背景图片不显示
**解决方案**:
- 确保 `assets/dmg-background.png` 文件存在
- 检查图片尺寸是否为 540x380
- 重新生成DMG

## 高级配置

### 1. 代码签名（可选）
如果你有Apple Developer账号，可以对应用进行代码签名：

```json
// 在 package.json 的 build.mac 中添加
"identity": "Developer ID Application: Your Name (TEAM_ID)"
```

### 2. 公证（可选）
对于分发给其他用户的应用，建议进行公证：

```json
// 在 package.json 的 build.mac 中添加
"hardenedRuntime": true,
"gatekeeperAssess": false,
"notarize": {
  "teamId": "YOUR_TEAM_ID"
}
```

### 3. 自动更新（可选）
可以集成 electron-updater 实现自动更新功能。

## 分发建议

1. **文件命名**: 使用有意义的版本号，如 `贪吃蛇游戏-v1.0.0.dmg`
2. **文件大小**: 通常在 100-200MB 之间
3. **测试**: 在不同的macOS版本上测试
4. **文档**: 提供安装和使用说明
5. **支持**: 准备常见问题解答

## 总结

完成以上步骤后，你就成功创建了一个可分发的macOS应用程序。DMG文件可以直接分享给其他用户安装使用。
