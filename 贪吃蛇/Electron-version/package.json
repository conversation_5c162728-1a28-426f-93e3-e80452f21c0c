{"name": "snake-game-electron", "version": "1.0.0", "description": "贪吃蛇游戏 - Electron桌面版", "main": "main.js", "author": "Your Name", "license": "MIT", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build:mac": "electron-builder --mac", "build:dmg": "electron-builder --mac dmg", "dist": "npm run build", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "build": {"appId": "com.yourname.snake-game", "productName": "贪吃蛇游戏", "directories": {"output": "dist"}, "compression": "maximum", "removePackageScripts": true, "files": ["main.js", "src/**/*", "!src/assets/sounds/**/*", "!**/*.md", "!**/*.txt"], "mac": {"category": "public.app-category.games", "icon": "assets/icon.icns", "identity": null, "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}]}, "dmg": {"title": "贪吃蛇游戏安装", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}], "window": {"width": 540, "height": 380}}, "win": {"target": "nsis", "icon": "assets/icon.ico"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}