# 贪吃蛇游戏 - Electron版本

这是基于HTML版本创建的Electron桌面应用版本。

## 功能特性

- 🎮 完整的贪吃蛇游戏体验
- 🖥️ 原生桌面应用界面
- ⌨️ 键盘快捷键支持
- 📱 响应式设计
- 🔊 音效支持
- ⚙️ 多难度设置
- 📦 可打包成dmg安装包

## 开发和运行

### 安装依赖
```bash
npm install
```

### 开发模式运行
```bash
npm run dev
```

### 正式模式运行
```bash
npm start
```

## 打包应用

### 打包所有平台
```bash
npm run build
```

### 仅打包macOS版本
```bash
npm run build:mac
```

### 打包成dmg安装包
```bash
npm run build:dmg
```

## 图标生成

应用图标位于 `assets/icon.svg`。要生成不同格式的图标文件，你需要：

1. **生成PNG图标**（用于Linux和Windows）：
   ```bash
   # 安装imagemagick（如果还没安装）
   brew install imagemagick
   
   # 生成不同尺寸的PNG图标
   convert assets/icon.svg -resize 512x512 assets/icon.png
   convert assets/icon.svg -resize 256x256 assets/<EMAIL>
   convert assets/icon.svg -resize 128x128 assets/<EMAIL>
   ```

2. **生成ICO图标**（用于Windows）：
   ```bash
   convert assets/icon.svg -resize 256x256 assets/icon.ico
   ```

3. **生成ICNS图标**（用于macOS）：
   ```bash
   # 创建iconset目录
   mkdir assets/icon.iconset
   
   # 生成不同尺寸的图标
   convert assets/icon.svg -resize 16x16 assets/icon.iconset/icon_16x16.png
   convert assets/icon.svg -resize 32x32 assets/icon.iconset/<EMAIL>
   convert assets/icon.svg -resize 32x32 assets/icon.iconset/icon_32x32.png
   convert assets/icon.svg -resize 64x64 assets/icon.iconset/<EMAIL>
   convert assets/icon.svg -resize 128x128 assets/icon.iconset/icon_128x128.png
   convert assets/icon.svg -resize 256x256 assets/icon.iconset/<EMAIL>
   convert assets/icon.svg -resize 256x256 assets/icon.iconset/icon_256x256.png
   convert assets/icon.svg -resize 512x512 assets/icon.iconset/<EMAIL>
   convert assets/icon.svg -resize 512x512 assets/icon.iconset/icon_512x512.png
   convert assets/icon.svg -resize 1024x1024 assets/icon.iconset/<EMAIL>
   
   # 生成icns文件
   iconutil -c icns assets/icon.iconset
   ```

## 键盘快捷键

- `Cmd/Ctrl + N`: 新游戏
- `Space`: 暂停/继续
- `Cmd/Ctrl + ,`: 打开设置
- `Cmd/Ctrl + R`: 重新加载
- `Cmd/Ctrl + Q`: 退出应用
- `F11` (Windows/Linux) / `Ctrl+Cmd+F` (macOS): 全屏

## 网页版到桌面版的适配

1. **安全策略**: 添加了Content Security Policy
2. **菜单集成**: 原生应用菜单和快捷键
3. **窗口管理**: 适当的窗口大小和最小尺寸
4. **生命周期**: 正确的应用启动和退出处理
5. **平台适配**: macOS特殊的菜单结构和行为

## 项目结构

```
Electron-version/
├── main.js              # Electron主进程
├── package.json          # 项目配置和依赖
├── src/                  # 游戏源码（从HTML版本复制）
│   ├── index.html
│   ├── css/
│   ├── js/
│   └── assets/
├── assets/               # 应用资源
│   ├── icon.svg         # 应用图标源文件
│   └── ...              # 其他图标文件
└── dist/                # 打包输出目录
```

## 故障排除

如果遇到打包问题，请检查：

1. 确保所有依赖都已正确安装
2. 确保图标文件存在且格式正确
3. 检查package.json中的build配置
4. 查看控制台输出的错误信息

## 许可证

MIT License
