<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="snakeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="foodGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF5722;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D32F2F;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="512" height="512" rx="64" fill="#1B5E20"/>
  
  <!-- 游戏边框 -->
  <rect x="64" y="64" width="384" height="384" rx="16" fill="#2E7D32" stroke="#4CAF50" stroke-width="4"/>
  
  <!-- 蛇身体 -->
  <rect x="120" y="200" width="32" height="32" rx="8" fill="url(#snakeGradient)"/>
  <rect x="152" y="200" width="32" height="32" rx="8" fill="url(#snakeGradient)"/>
  <rect x="184" y="200" width="32" height="32" rx="8" fill="url(#snakeGradient)"/>
  <rect x="216" y="200" width="32" height="32" rx="8" fill="url(#snakeGradient)"/>
  <rect x="248" y="200" width="32" height="32" rx="8" fill="url(#snakeGradient)"/>
  <rect x="280" y="200" width="32" height="32" rx="8" fill="url(#snakeGradient)"/>
  
  <!-- 蛇头 -->
  <rect x="312" y="200" width="32" height="32" rx="12" fill="url(#snakeGradient)"/>
  
  <!-- 蛇眼睛 -->
  <circle cx="320" cy="208" r="3" fill="white"/>
  <circle cx="336" cy="208" r="3" fill="white"/>
  <circle cx="320" cy="208" r="1.5" fill="black"/>
  <circle cx="336" cy="208" r="1.5" fill="black"/>
  
  <!-- 食物 -->
  <circle cx="200" cy="280" r="12" fill="url(#foodGradient)"/>
  <rect x="196" y="268" width="8" height="8" rx="2" fill="#4CAF50"/>
  
  <!-- 标题文字 -->
  <text x="256" y="400" text-anchor="middle" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="white">🐍</text>
</svg>
