#!/bin/bash

# 图标生成脚本
# 需要先安装 ImageMagick: brew install imagemagick

echo "开始生成应用图标..."

# 检查ImageMagick是否安装
if ! command -v convert &> /dev/null; then
    echo "错误: ImageMagick未安装"
    echo "请运行: brew install imagemagick"
    exit 1
fi

# 检查源SVG文件是否存在
if [ ! -f "icon.svg" ]; then
    echo "错误: icon.svg文件不存在"
    exit 1
fi

echo "生成PNG图标..."
convert icon.svg -resize 512x512 icon.png
convert icon.svg -resize 256x256 <EMAIL>
convert icon.svg -resize 128x128 <EMAIL>

echo "生成ICO图标（Windows）..."
convert icon.svg -resize 256x256 icon.ico

echo "生成ICNS图标（macOS）..."
# 创建iconset目录
mkdir -p icon.iconset

# 生成不同尺寸的图标
convert icon.svg -resize 16x16 icon.iconset/icon_16x16.png
convert icon.svg -resize 32x32 icon.iconset/<EMAIL>
convert icon.svg -resize 32x32 icon.iconset/icon_32x32.png
convert icon.svg -resize 64x64 icon.iconset/<EMAIL>
convert icon.svg -resize 128x128 icon.iconset/icon_128x128.png
convert icon.svg -resize 256x256 icon.iconset/<EMAIL>
convert icon.svg -resize 256x256 icon.iconset/icon_256x256.png
convert icon.svg -resize 512x512 icon.iconset/<EMAIL>
convert icon.svg -resize 512x512 icon.iconset/icon_512x512.png
convert icon.svg -resize 1024x1024 icon.iconset/<EMAIL>

# 生成icns文件
iconutil -c icns icon.iconset

# 清理临时文件
rm -rf icon.iconset

echo "图标生成完成！"
echo "生成的文件:"
echo "  - icon.png (512x512)"
echo "  - <EMAIL> (256x256)"
echo "  - <EMAIL> (128x128)"
echo "  - icon.ico (Windows)"
echo "  - icon.icns (macOS)"
