{"name": "fs-extra", "version": "10.1.0", "description": "fs-extra contains methods that aren't included in the vanilla Node.js fs package. Such as recursive mkdir, copy, and remove.", "engines": {"node": ">=12"}, "homepage": "https://github.com/jprichardson/node-fs-extra", "repository": {"type": "git", "url": "https://github.com/jprichardson/node-fs-extra"}, "keywords": ["fs", "file", "file system", "copy", "directory", "extra", "mkdirp", "mkdir", "mkdirs", "recursive", "json", "read", "write", "extra", "delete", "remove", "touch", "create", "text", "output", "move", "promise"], "author": "<PERSON> <jp<PERSON><PERSON><EMAIL>>", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "devDependencies": {"at-least-node": "^1.0.0", "klaw": "^2.1.1", "klaw-sync": "^3.0.2", "minimist": "^1.1.1", "mocha": "^5.0.5", "nyc": "^15.0.0", "proxyquire": "^2.0.1", "read-dir-files": "^0.1.1", "standard": "^16.0.3"}, "main": "./lib/index.js", "files": ["lib/", "!lib/**/__tests__/"], "scripts": {"lint": "standard", "test-find": "find ./lib/**/__tests__ -name *.test.js | xargs mocha", "test": "npm run lint && npm run unit", "unit": "nyc node test.js"}, "sideEffects": false}