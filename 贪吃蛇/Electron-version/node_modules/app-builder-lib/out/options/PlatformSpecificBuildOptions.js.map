{"version": 3, "file": "PlatformSpecificBuildOptions.js", "sourceRoot": "", "sources": ["../../src/options/PlatformSpecificBuildOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import { CompressionLevel, Publish, TargetConfiguration, TargetSpecificOptions } from \"../core\"\nimport { FileAssociation } from \"./FileAssociation\"\n\nexport interface FileSet {\n  /**\n   * The source path relative to the project directory.\n   */\n  from?: string\n  /**\n   * The destination path relative to the app's content directory for `extraFiles` and the app's resource directory for `extraResources`.\n   */\n  to?: string\n  /**\n   * The [glob patterns](/file-patterns).\n   */\n  filter?: Array<string> | string\n}\n\nexport interface AsarOptions {\n  /**\n   * Whether to automatically unpack executables files.\n   * @default true\n   */\n  smartUnpack?: boolean\n\n  ordering?: string | null\n}\n\nexport interface PlatformSpecificBuildOptions extends TargetSpecificOptions {\n  /**\n   * The application id. Used as [CFBundleIdentifier](https://developer.apple.com/library/ios/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/uid/20001431-102070) for MacOS and as\n   * [Application User Model ID](https://msdn.microsoft.com/en-us/library/windows/desktop/dd378459(v=vs.85).aspx) for Windows (NSIS target only, Squirrel.Windows not supported). It is strongly recommended that an explicit ID is set.\n   * @default com.electron.${name}\n   */\n  readonly appId?: string | null\n\n  /**\n   * The [artifact file name template](/configuration/configuration#artifact-file-name-template). Defaults to `${productName}-${version}.${ext}` (some target can have other defaults, see corresponding options).\n   */\n  readonly artifactName?: string | null\n\n  /**\n   * The executable name. Defaults to `productName`.\n   */\n  readonly executableName?: string | null\n\n  /**\n   * The compression level. If you want to rapidly test build, `store` can reduce build time significantly. `maximum` doesn't lead to noticeable size difference, but increase build time.\n   * @default normal\n   */\n  readonly compression?: CompressionLevel | null\n\n  files?: Array<FileSet | string> | FileSet | string | null\n  extraResources?: Array<FileSet | string> | FileSet | string | null\n  extraFiles?: Array<FileSet | string> | FileSet | string | null\n\n  /**\n   * Whether to package the application's source code into an archive, using [Electron's archive format](http://electron.atom.io/docs/tutorial/application-packaging/).\n   *\n   * Node modules, that must be unpacked, will be detected automatically, you don't need to explicitly set [asarUnpack](#configuration-asarUnpack) - please file an issue if this doesn't work.\n   * @default true\n   */\n  readonly asar?: AsarOptions | boolean | null\n\n  /**\n   * A [glob patterns](/file-patterns) relative to the [app directory](#MetadataDirectories-app), which specifies which files to unpack when creating the [asar](http://electron.atom.io/docs/tutorial/application-packaging/) archive.\n   */\n  readonly asarUnpack?: Array<string> | string | null\n\n  /** @private */\n  readonly icon?: string | null\n\n  /**\n   * The file associations.\n   */\n  readonly fileAssociations?: Array<FileAssociation> | FileAssociation\n  /**\n   * The URL protocol schemes.\n   */\n  readonly protocols?: Array<Protocol> | Protocol\n\n  /**\n   * The electron locales to keep. By default, all Electron locales used as-is.\n   */\n  readonly electronLanguages?: Array<string> | string\n\n  /**\n   * Whether to fail if app will be not code signed.\n   */\n  readonly forceCodeSigning?: boolean\n\n  /**\n   * The [electron-updater compatibility](/auto-update#compatibility) semver range.\n   */\n  readonly electronUpdaterCompatibility?: string | null\n\n  publish?: Publish\n\n  /**\n   * Whether to infer update channel from application version pre-release components. e.g. if version `0.12.1-alpha.1`, channel will be set to `alpha`. Otherwise to `latest`.\n   * @default true\n   */\n  readonly detectUpdateChannel?: boolean\n\n  /**\n   * Please see [Building and Releasing using Channels](https://github.com/electron-userland/electron-builder/issues/1182#issuecomment-324947139).\n   * @default false\n   */\n  readonly generateUpdatesFilesForAllChannels?: boolean\n\n  /**\n   * The release info. Intended for command line usage:\n   *\n   * ```\n   * -c.releaseInfo.releaseNotes=\"new features\"\n   * ```\n   */\n  readonly releaseInfo?: ReleaseInfo\n\n  readonly target?: Array<string | TargetConfiguration> | string | TargetConfiguration | null\n\n  /** @private */\n  cscLink?: string | null\n\n  /** @private */\n  cscKeyPassword?: string | null\n\n  readonly defaultArch?: string\n}\n\nexport interface ReleaseInfo {\n  /**\n   * The release name.\n   */\n  releaseName?: string | null\n\n  /**\n   * The release notes.\n   */\n  releaseNotes?: string | null\n\n  /**\n   * The path to release notes file. Defaults to `release-notes-${platform}.md` (where `platform` it is current platform — `mac`, `linux` or `windows`) or `release-notes.md` in the [build resources](#MetadataDirectories-buildResources).\n   */\n  releaseNotesFile?: string | null\n\n  /**\n   * The release date.\n   */\n  releaseDate?: string\n\n  /**\n   * Vendor specific information.\n   */\n  vendor?: { [key: string]: any } | null\n}\n\n/**\n * URL Protocol Schemes. Protocols to associate the app with. macOS only.\n *\n * Please note — on macOS [you need to register an `open-url` event handler](http://electron.atom.io/docs/api/app/#event-open-url-macos).\n */\nexport interface Protocol {\n  /**\n   * The name. e.g. `IRC server URL`.\n   */\n  readonly name: string\n\n  /**\n   * The schemes. e.g. `[\"irc\", \"ircs\"]`.\n   */\n  readonly schemes: Array<string>\n\n  /**\n   * *macOS-only* The app’s role with respect to the type.\n   * @default Editor\n   */\n  readonly role?: \"Editor\" | \"Viewer\" | \"Shell\" | \"None\"\n}\n"]}