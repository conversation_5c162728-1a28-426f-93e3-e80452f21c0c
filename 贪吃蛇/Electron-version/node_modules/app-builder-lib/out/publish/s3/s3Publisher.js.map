{"version": 3, "file": "s3Publisher.js", "sourceRoot": "", "sources": ["../../../src/publish/s3/s3Publisher.ts"], "names": [], "mappings": ";;AAAA,+CAAgF;AAGhF,uDAAmD;AAEnD,MAAqB,WAAY,SAAQ,iCAAe;IAGtD,YACE,OAAuB,EACN,IAAe;QAEhC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QAFH,SAAI,GAAJ,IAAI,CAAW;QAJzB,iBAAY,GAAG,IAAI,CAAA;IAO5B,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,OAAkB,EAAE,qBAAoC,EAAE,aAAsB;QAClH,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QAC7B,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,MAAM,IAAI,wCAAyB,CAAC,mDAAmD,CAAC,CAAA;QAC1F,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;YAC/E,yHAAyH;YACzH,IAAI,CAAC;gBACH,OAAO,CAAC,MAAM,GAAG,MAAM,IAAA,gCAAiB,EAAC,CAAC,qBAAqB,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,CAAA;YACvF,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,aAAa,EAAE,CAAC;oBAClB,MAAM,CAAC,CAAA;gBACT,CAAC;qBAAM,CAAC;oBACN,kBAAG,CAAC,IAAI,CAAC,yHAAyH,CAAC,EAAE,CAAC,CAAA;gBACxI,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,IAAI,qBAAqB,IAAI,IAAI,EAAE,CAAC;YAC7D,OAAO,CAAC,OAAO,GAAG,qBAAqB,CAAA;QACzC,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/D,CAAC;YAAC,OAAe,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;QAC5D,CAAC;IACH,CAAC;IAES,aAAa;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;IACzB,CAAC;IAES,kBAAkB,CAAC,IAAmB;QAC9C,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;QAE9B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC7C,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACzC,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QACrD,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACjD,CAAC;IACH,CAAC;IAED,QAAQ;QACN,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAA;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA;QACnC,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,eAAe,QAAQ,GAAG,CAAA;QAC5E,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;CACF;AApED,8BAoEC", "sourcesContent": ["import { executeAppBuilder, InvalidConfigurationError, log } from \"builder-util\"\nimport { S3Options } from \"builder-util-runtime\"\nimport { PublishContext } from \"electron-publish\"\nimport { BaseS3Publisher } from \"./BaseS3Publisher\"\n\nexport default class S3Publisher extends BaseS3Publisher {\n  readonly providerName = \"s3\"\n\n  constructor(\n    context: PublishContext,\n    private readonly info: S3Options\n  ) {\n    super(context, info)\n  }\n\n  static async checkAndResolveOptions(options: S3Options, channelFromAppVersion: string | null, errorIfCannot: boolean) {\n    const bucket = options.bucket\n    if (bucket == null) {\n      throw new InvalidConfigurationError(`Please specify \"bucket\" for \"s3\" publish provider`)\n    }\n\n    if (options.endpoint == null && bucket.includes(\".\") && options.region == null) {\n      // on dotted bucket names, we need to use a path-based endpoint URL. Path-based endpoint URLs need to include the region.\n      try {\n        options.region = await executeAppBuilder([\"get-bucket-location\", \"--bucket\", bucket])\n      } catch (e: any) {\n        if (errorIfCannot) {\n          throw e\n        } else {\n          log.warn(`cannot compute region for bucket (required because on dotted bucket names, we need to use a path-based endpoint URL): ${e}`)\n        }\n      }\n    }\n\n    if (options.channel == null && channelFromAppVersion != null) {\n      options.channel = channelFromAppVersion\n    }\n\n    if (options.endpoint != null && options.endpoint.endsWith(\"/\")) {\n      ;(options as any).endpoint = options.endpoint.slice(0, -1)\n    }\n  }\n\n  protected getBucketName(): string {\n    return this.info.bucket\n  }\n\n  protected configureS3Options(args: Array<string>): void {\n    super.configureS3Options(args)\n\n    if (this.info.endpoint != null) {\n      args.push(\"--endpoint\", this.info.endpoint)\n    }\n    if (this.info.region != null) {\n      args.push(\"--region\", this.info.region)\n    }\n\n    if (this.info.storageClass != null) {\n      args.push(\"--storageClass\", this.info.storageClass)\n    }\n    if (this.info.encryption != null) {\n      args.push(\"--encryption\", this.info.encryption)\n    }\n  }\n\n  toString() {\n    const result = super.toString()\n    const endpoint = this.info.endpoint\n    if (endpoint != null) {\n      return result.substring(0, result.length - 1) + `, endpoint: ${endpoint})`\n    }\n    return result\n  }\n}\n"]}