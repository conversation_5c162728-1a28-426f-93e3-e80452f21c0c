{"version": 3, "file": "unpackDetector.js", "sourceRoot": "", "sources": ["../../src/asar/unpackDetector.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,+CAAkC;AAClC,4CAAiD;AACjD,uCAAgC;AAChC,+CAA+C;AAC/C,6BAA4B;AAC5B,wDAAyD;AACzD,yDAA2E;AAE3E,SAAS,QAAQ,CAAC,GAA+B,EAAE,GAAW,EAAE,KAAa;IAC3E,IAAI,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IACvB,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,IAAI,GAAG,CAAC,KAAK,CAAC,CAAA;QACd,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;IACpB,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAClB,CAAC;AACH,CAAC;AAED,SAAgB,UAAU,CAAC,IAAY;IACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;AAC1G,CAAC;AAFD,gCAEC;AAED,gBAAgB;AACT,KAAK,UAAU,kBAAkB,CAAC,OAAwB,EAAE,cAA2B,EAAE,YAAoB,EAAE,0BAAkC;IACtJ,MAAM,WAAW,GAAG,IAAI,GAAG,EAAyB,CAAA;IACpD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;IAEjC,SAAS,UAAU,CAAC,KAAa,EAAE,IAAY;QAC7C,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QAC3B,IAAI,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAM;QACR,CAAC;QAED,GAAG,CAAC;YACF,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;YACzB,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;YAC7B,oFAAoF;YACpF,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;YAE9C,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1D,MAAK;YACP,CAAC;YACD,KAAK,GAAG,CAAC,CAAA;QACX,CAAC,QAAQ,IAAI,EAAC;QAEd,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAC1B,CAAC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QACrD,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,sCAAoB,CAAC,CAAA;QACpD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACd,SAAQ;QACV,CAAC;QAED,IAAI,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,sCAAoB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QACpF,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;YACvB,SAAQ;QACV,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,GAAG,sCAAoB,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;YACtD,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,GAAG,CAAC,CAAC,CAAA;QAC7D,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC,MAAM,EAAE,EAAE,CAAC;YAClC,SAAQ;QACV,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,CAAA;QACpD,MAAM,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,0BAA0B,EAAE,IAAA,kCAAkB,EAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAA;QAClH,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,0BAA0B,EAAE,IAAA,kCAAkB,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAA;QAClG,IAAI,cAAc,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,CAAC;YAChD,qDAAqD;YACrD,UAAU,CAAC,aAAa,EAAE,uBAAuB,CAAC,CAAA;YAClD,SAAQ;QACV,CAAC;QAED,oEAAoE;QACpE,IAAI,YAAY,GAAG,KAAK,CAAA;QACxB,uEAAuE;QACvE,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;QAC5C,IAAI,UAAU,KAAK,gBAAgB,IAAI,UAAU,KAAK,eAAe,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1F,YAAY,GAAG,IAAI,CAAA;QACrB,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,cAAc,CAAC,EAAE,CAAC;YAC/C,YAAY,GAAG,CAAC,CAAC,IAAA,+BAAgB,EAAC,IAAI,CAAC,CAAA;QACzC,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,SAAQ;QACV,CAAC;QAED,IAAI,kBAAG,CAAC,cAAc,EAAE,CAAC;YACvB,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,0BAA0B,EAAE,EAAE,8BAA8B,CAAC,CAAA;QACxG,CAAC;QAED,UAAU,CAAC,aAAa,EAAE,uBAAuB,CAAC,CAAA;IACpD,CAAC;IAED,IAAI,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;QACzB,MAAM,IAAA,gBAAK,EAAC,GAAG,YAAY,GAAG,IAAI,CAAC,GAAG,cAAc,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QAC1E,sGAAsG;QACtG,MAAM,sBAAe,CAAC,GAAG,CACvB,WAAW,CAAC,IAAI,EAAE,EAClB,KAAK,EAAC,SAAS,EAAC,EAAE;YAChB,MAAM,IAAI,GAAG,YAAY,GAAG,IAAI,CAAC,GAAG,GAAG,SAAS,CAAA;YAChD,MAAM,IAAA,gBAAK,EAAC,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;YACtC,MAAM,sBAAe,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAE,EAAE,CAAC,EAAE,EAAO,EAAE;gBAClE,IAAI,WAAW,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC;oBAC/C,kBAAkB;oBAClB,OAAO,IAAI,CAAA;gBACb,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAA,gBAAK,EAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;gBACzD,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC,EACD,gBAAW,CACZ,CAAA;IACH,CAAC;AACH,CAAC;AA/FD,gDA+FC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { log } from \"builder-util\"\nimport { CONCURRENCY } from \"builder-util/out/fs\"\nimport { mkdir } from \"fs-extra\"\nimport { isBinaryFileSync } from \"isbinaryfile\"\nimport * as path from \"path\"\nimport { NODE_MODULES_PATTERN } from \"../fileTransformer\"\nimport { getDestinationPath, ResolvedFileSet } from \"../util/appFileCopier\"\n\nfunction addValue(map: Map<string, Array<string>>, key: string, value: string) {\n  let list = map.get(key)\n  if (list == null) {\n    list = [value]\n    map.set(key, list)\n  } else {\n    list.push(value)\n  }\n}\n\nexport function isLibOrExe(file: string): boolean {\n  return file.endsWith(\".dll\") || file.endsWith(\".exe\") || file.endsWith(\".dylib\") || file.endsWith(\".so\")\n}\n\n/** @internal */\nexport async function detectUnpackedDirs(fileSet: ResolvedFileSet, autoUnpackDirs: Set<string>, unpackedDest: string, rootForAppFilesWithoutAsar: string) {\n  const dirToCreate = new Map<string, Array<string>>()\n  const metadata = fileSet.metadata\n\n  function addParents(child: string, root: string) {\n    child = path.dirname(child)\n    if (autoUnpackDirs.has(child)) {\n      return\n    }\n\n    do {\n      autoUnpackDirs.add(child)\n      const p = path.dirname(child)\n      // create parent dir to be able to copy file later without directory existence check\n      addValue(dirToCreate, p, path.basename(child))\n\n      if (child === root || p === root || autoUnpackDirs.has(p)) {\n        break\n      }\n      child = p\n    } while (true)\n\n    autoUnpackDirs.add(root)\n  }\n\n  for (let i = 0, n = fileSet.files.length; i < n; i++) {\n    const file = fileSet.files[i]\n    const index = file.lastIndexOf(NODE_MODULES_PATTERN)\n    if (index < 0) {\n      continue\n    }\n\n    let nextSlashIndex = file.indexOf(path.sep, index + NODE_MODULES_PATTERN.length + 1)\n    if (nextSlashIndex < 0) {\n      continue\n    }\n\n    if (file[index + NODE_MODULES_PATTERN.length] === \"@\") {\n      nextSlashIndex = file.indexOf(path.sep, nextSlashIndex + 1)\n    }\n\n    if (!metadata.get(file)!.isFile()) {\n      continue\n    }\n\n    const packageDir = file.substring(0, nextSlashIndex)\n    const packageDirPathInArchive = path.relative(rootForAppFilesWithoutAsar, getDestinationPath(packageDir, fileSet))\n    const pathInArchive = path.relative(rootForAppFilesWithoutAsar, getDestinationPath(file, fileSet))\n    if (autoUnpackDirs.has(packageDirPathInArchive)) {\n      // if package dir is unpacked, any file also unpacked\n      addParents(pathInArchive, packageDirPathInArchive)\n      continue\n    }\n\n    // https://github.com/electron-userland/electron-builder/issues/2679\n    let shouldUnpack = false\n    // ffprobe-static and ffmpeg-static are known packages to always unpack\n    const moduleName = path.basename(packageDir)\n    if (moduleName === \"ffprobe-static\" || moduleName === \"ffmpeg-static\" || isLibOrExe(file)) {\n      shouldUnpack = true\n    } else if (!file.includes(\".\", nextSlashIndex)) {\n      shouldUnpack = !!isBinaryFileSync(file)\n    }\n\n    if (!shouldUnpack) {\n      continue\n    }\n\n    if (log.isDebugEnabled) {\n      log.debug({ file: pathInArchive, reason: \"contains executable code\" }, \"not packed into asar archive\")\n    }\n\n    addParents(pathInArchive, packageDirPathInArchive)\n  }\n\n  if (dirToCreate.size > 0) {\n    await mkdir(`${unpackedDest + path.sep}node_modules`, { recursive: true })\n    // child directories should be not created asynchronously - parent directories should be created first\n    await BluebirdPromise.map(\n      dirToCreate.keys(),\n      async parentDir => {\n        const base = unpackedDest + path.sep + parentDir\n        await mkdir(base, { recursive: true })\n        await BluebirdPromise.each(dirToCreate.get(parentDir)!, (it): any => {\n          if (dirToCreate.has(parentDir + path.sep + it)) {\n            // already created\n            return null\n          } else {\n            return mkdir(base + path.sep + it, { recursive: true })\n          }\n        })\n      },\n      CONCURRENCY\n    )\n  }\n}\n"]}