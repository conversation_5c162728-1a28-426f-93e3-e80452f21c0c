{"version": 3, "file": "NsisTarget.js", "sourceRoot": "", "sources": ["../../../src/targets/nsis/NsisTarget.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,+CAAgL;AAChL,+DAA4H;AAC5H,4CAA8D;AAC9D,iCAA0B;AAC1B,yBAAwB;AACxB,uCAAiD;AACjD,6BAA4B;AAC5B,mDAAiD;AACjD,qCAAmC;AACnC,2GAAsH;AACtH,6DAAsF;AACtF,0CAA0C;AAC1C,0DAAyD;AACzD,4CAAuC;AACvC,qCAAqC;AAErC,wCAAoD;AACpD,oFAAgK;AAChK,8CAAmG;AAGnG,yCAA+F;AAC/F,+CAAkD;AAElD,+DAA2D;AAC3D,yCAAgH;AAEhH,MAAM,KAAK,GAAG,IAAA,eAAM,EAAC,uBAAuB,CAAC,CAAA;AAE7C,uCAAuC;AACvC,MAAM,wBAAwB,GAAG,2BAAI,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAA;AAEnF,uCAAuC;AACvC,MAAM,uBAAuB,GAAG,GAAG,EAAE,CAAC,IAAA,2BAAa,EAAC,gBAAgB,EAAE,OAAO,EAAE,0FAA0F,CAAC,CAAA;AAE1K,MAAM,4BAA4B,GAAG,KAAK,CAAA;AAE1C,MAAa,UAAW,SAAQ,aAAM;IAOpC,YACW,QAAqB,EACrB,MAAc,EACvB,UAAkB,EACC,aAA+B;QAElD,KAAK,CAAC,UAAU,CAAC,CAAA;QALR,aAAQ,GAAR,QAAQ,CAAa;QACrB,WAAM,GAAN,MAAM,CAAQ;QAEJ,kBAAa,GAAb,aAAa,CAAkB;QARpD,eAAe;QACN,UAAK,GAAsB,IAAI,GAAG,EAAE,CAAA;QACpC,qBAAgB,GAAG,KAAK,CAAA;QAU/B,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAA;QAE7B,IAAI,CAAC,OAAO;YACV,UAAU,KAAK,UAAU;gBACvB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;gBACrB,CAAC,CAAC;oBACE,2BAA2B,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;oBACtG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI;iBAC7B,CAAA;QAEP,IAAI,UAAU,KAAK,MAAM,EAAE,CAAC;YAC1B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAG,IAAI,CAAC,QAAQ,CAAC,MAAc,CAAC,UAAU,KAAK,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAA;QAChH,CAAC;QAED,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAA;QAChD,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,2BAA2B,CAAC,IAAI,IAAI,EAAE,CAAC;YAC9D,kBAAG,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAA;QAC7E,CAAC;QAED,4BAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IACzC,CAAC;IAED,KAAK,CAAC,SAAiB,EAAE,IAAU;QACjC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;QAC/B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC1B,CAAC;IAED,IAAI,wBAAwB;QAC1B,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,KAAK,KAAK,CAAA;IACvE,CAAC;IAEO,8BAA8B;QACpC,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAA;QACpF,OAAO,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,sBAAO,EAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAA;IAChG,CAAC;IAED,eAAe;IACf,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,IAAU;QACjD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAE9B,MAAM,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAA;QAC9D,MAAM,MAAM,GAAG,CAAC,wBAAwB,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;QACzE,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,IAAI,mBAAI,CAAC,IAAI,CAAC,SAAS,MAAM,EAAE,CAAC,CAAA;QACxI,MAAM,2BAA2B,GAAG,IAAI,CAAC,8BAA8B,EAAE,CAAA;QACzE,MAAM,cAAc,GAAmB;YACrC,UAAU,EAAE,IAAI;YAChB,WAAW,EAAE,QAAQ,CAAC,WAAW;YACjC,QAAQ,EAAE,2BAA2B,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,2BAA2B,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;SACvG,CAAA;QAED,MAAM,KAAK,GAAG,IAAA,YAAI,EAAC,iBAAiB,mBAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACjD,MAAM,IAAA,iBAAO,EAAC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,wBAAwB,CAAC,CAAC,CAAC,IAAA,wEAAwC,EAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAA;QACnJ,KAAK,CAAC,GAAG,EAAE,CAAA;QAEX,IAAI,wBAAwB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACpD,MAAM,IAAI,GAAG,MAAM,IAAA,8CAAc,EAAC,WAAW,CAAC,CAAA;YAC9C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,EAAE,WAAW;aAClB,CAAA;QACH,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,qBAAqB,CAAC,WAAW,CAAC,CAAA;QACjD,CAAC;IACH,CAAC;IAED,IAAc,wBAAwB;QACpC,6CAA6C;QAC7C,OAAO,iBAAiB,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,mBAAmB,CAAA;IACpF,CAAC;IAED,IAAY,UAAU;QACpB,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,CAAA;IACjC,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAA;YACpG,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;YACpC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBACvD,CAAC;gBAAA,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;YAC7F,CAAC;YACD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,EAAE,CAAA;YACpC,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;gBACjC,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YAClC,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAA;QACxC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,KAAwB;;QACnD,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;QACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAA;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,MAAM,iBAAiB,GAAG,QAAQ,CAAC,yBAAyB,CAC1D,OAAO,EACP,KAAK,EACL,WAAW,EACX,IAAI,CAAC,wBAAwB,EAC7B,KAAK,EACL,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC,WAAW,CACvD,CAAA;QACD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAA;QAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAA;QAE/D,MAAM,SAAS,GAAQ;YACrB,MAAM,EAAE,IAAI,CAAC,IAAI;YACjB,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,aAAa,CAAC;YACjC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;iBAC5B,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,mBAAI,CAAC,EAAE,CAAC,CAAC;iBACnB,IAAI,CAAC,IAAI,CAAC;SACd,CAAA;QACD,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,KAAK,IAAI,CAAA;QAEhD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAA;YAC7B,SAAS,CAAC,UAAU,GAAG,YAAY,CAAA;QACrC,CAAC;QAED,MAAM,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAC1C;YACE,qBAAqB,EAAE,IAAI,CAAC,IAAI;YAChC,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,WAAW;SAClB,EACD,SAAS,CACV,CAAA;QAED,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,2BAAI,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAA;QAC1E,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAClD,MAAM,OAAO,GAAY;YACvB,MAAM,EAAE,OAAO,CAAC,EAAE;YAClB,QAAQ,EAAE,IAAI;YACd,yGAAyG;YACzG,iBAAiB,EAAE,eAAe;YAClC,YAAY,EAAE,OAAO,CAAC,WAAW;YACjC,gBAAgB,EAAE,OAAO,CAAC,eAAe;YACzC,YAAY,EAAE,IAAA,0CAA6B,EAAC,OAAO,EAAE,CAAC,QAAQ,IAAI,YAAY,CAAC;YAC/E,eAAe,EAAE,OAAO,CAAC,WAAW;YACpC,OAAO,EAAE,OAAO,CAAC,OAAO;YAExB,WAAW,EAAE,QAAQ,CAAC,UAAU;YAChC,mBAAmB,EAAE,QAAQ,CAAC,IAAI,CAAC,iBAAiB;YAEpD,gBAAgB,EAAE,IAAA,iDAAoC,EAAC,OAAO,CAAC,IAAI,CAAC;SACrE,CAAA;QACD,IAAI,MAAA,OAAO,CAAC,gBAAgB,0CAAE,YAAY,EAAE,CAAC;YAC3C,OAAO,CAAC,+BAA+B,GAAG,IAAI,CAAA;QAChD,CAAC;QACD,IAAI,eAAe,KAAK,IAAI,EAAE,CAAC;YAC7B,OAAO,CAAC,wBAAwB,GAAG,4DAA4D,IAAI,EAAE,CAAA;QACvG,CAAC;QAED,MAAM,QAAQ,GAAa;YACzB,OAAO,EAAE,IAAI,aAAa,GAAG;YAC7B,gBAAgB,EAAE,OAAO,CAAC,4BAA4B,EAAE;YACxD,eAAe,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACzC,OAAO,EAAE,IAAI,CAAC,gBAAgB;SAC/B,CAAA;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;QAClC,MAAM,QAAQ,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAA;QAC/I,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,UAAU,EAAE,CAAC;gBACf,QAAQ,CAAC,IAAI,GAAG,IAAI,QAAQ,GAAG,CAAA;YACjC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;gBAC3B,OAAO,CAAC,UAAU,GAAG,QAAQ,CAAA;YAC/B,CAAC;QACH,CAAC;QAED,MAAM,YAAY,GAAwC,EAAE,CAAA;QAC5D,IAAI,aAAa,GAAG,CAAC,CAAA;QACrB,IAAI,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACtC,KAAK,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC1C,OAAO,CAAC,IAAI,KAAK,mBAAI,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,KAAK,mBAAI,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,GAAG,CAAA;YACxG,CAAC;QACH,CAAC;aAAM,IAAI,4BAA4B,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC5D,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAA;QAC9D,CAAC;aAAM,CAAC;YACN,MAAM,sBAAe,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,EAAC,IAAI,EAAC,EAAE;gBACnD,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;gBAChF,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;gBAC1B,MAAM,SAAS,GAAG,IAAI,KAAK,mBAAI,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,KAAK,mBAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAA;gBAC7F,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAA;gBACzB,4EAA4E;gBAC5E,MAAM,aAAa,GAAG,GAAG,SAAS,OAA2D,CAAA;gBAC7F,OAAO,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;gBAC5C,mCAAmC;gBACnC,4EAA4E;gBAC5E,MAAM,aAAa,GAAG,GAAG,SAAS,OAA2D,CAAA;gBAC7F,OAAO,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAA;gBAC7F,iEAAiE;gBACjE,4EAA4E;gBAC5E,MAAM,qBAAqB,GAAG,GAAG,SAAS,gBAA+F,CAAA;gBACzI,OAAO,CAAC,qBAAqB,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAA;gBAE1E,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACxB,MAAM,QAAQ,CAAC,uBAAuB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;oBACxD,YAAY,CAAC,mBAAI,CAAC,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAA;gBACrC,CAAC;gBACD,MAAM,OAAO,GAAG,MAAM,IAAA,yBAAU,GAAE,CAAA;gBAClC,MAAM,WAAW,GAAG,CAAC,MAAM,IAAA,mBAAI,EAAC,OAAO,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;gBAC7D,wEAAwE;gBACxE,MAAM,KAAK,GAAG,2BAA2B,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBAC3D,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;oBAClB,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,oCAAoC,CAAC,CAAA;gBACzE,CAAC;qBAAM,CAAC;oBACN,aAAa,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;gBACzC,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC,CAAA;QACnD,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,EAAE,aAAa,EAAE,qBAAqB,EAAE,WAAW,EAAE,GAAG,OAA0B,CAAA;YACxF,OAAO,CAAC,uBAAuB,GAAG,qBAAqB,IAAI,MAAM,CAAA;YAEjE,oEAAoE;YACpE,IAAI,OAAO,aAAa,KAAK,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxD,OAAO,CAAC,eAAe,GAAG,aAAa,IAAI,CAAC,MAAM,IAAA,gCAAiB,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;YACjF,CAAC;YAED,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;gBACxB,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;YACvE,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;QAChD,CAAC;QAED,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;YACxB,QAAQ;YACR,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,CAAA;QAC3D,CAAC;QAED,IAAI,QAAQ,CAAC,WAAW,KAAK,OAAO,EAAE,CAAC;YACrC,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAA;QAC9B,CAAC;aAAM,CAAC;YACN,8EAA8E;YAC9E,4IAA4I;YAC5I,+IAA+I;YAC/I,QAAQ,CAAC,aAAa,GAAG,MAAM,CAAA;YAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACzB,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAA;YAC3B,CAAC;QACH,CAAC;QAED,KAAK,CAAC,OAAO,CAAC,CAAA;QACd,KAAK,CAAC,QAAQ,CAAC,CAAA;QAEf,IAAI,QAAQ,CAAC,eAAe,CAAC,uBAAuB,IAAI,IAAI,IAAI,CAAC,MAAM,QAAQ,CAAC,eAAe,CAAC,uBAAuB,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;YAC9I,OAAM;QACR,CAAC;QAED,gIAAgI;QAChI,MAAM,kBAAkB,GAAG,EAAE,GAAG,OAAO,EAAE,CAAA;QACzC,MAAM,mBAAmB,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAA;QAC3C,IAAI,OAAO,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC;YACjC,kBAAkB,CAAC,OAAO,GAAG,OAAO,CAAC,YAAY,CAAA;YACjD,mBAAmB,CAAC,gBAAgB,GAAG,OAAO,CAAC,mBAAmB,CAAA;YAClE,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;QACpE,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kCAAkC,EAAE,CAAA;QACpE,MAAM,MAAM,GAAG,UAAU;YACvB,CAAC,CAAC,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,2BAAgB,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC;YACrE,CAAC,CAAC,MAAM,IAAI,CAAC,+BAA+B,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,aAAa,EAAE,YAAY,EAAE,KAAK,CAAC,CAAA;QAE3H,mJAAmJ;QACnJ,OAAO,CAAC,oBAAoB,GAAG,kBAAkB,CAAC,oBAAoB,CAAA;QAEtE,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,GAAG,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAA;QAClH,MAAM,OAAO,CAAC,GAAG,CAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,OAAO,CAAC,oBAAoB,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAA,iBAAM,EAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAA;QAEvJ,MAAM,gBAAgB,GAAG,IAAA,kDAA+B,EAAC,iBAAiB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAA;QACrH,IAAI,UAAe,CAAA;QACnB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,UAAU,GAAG,IAAA,mEAAmC,EAAC,aAAa,EAAE,YAAY,CAAC,CAAA;QAC/E,CAAC;aAAM,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACzC,UAAU,GAAG,MAAM,IAAA,8CAAc,EAAC,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAA;QACpF,CAAC;QAED,IAAI,UAAU,IAAI,IAAI,IAAI,YAAY,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAClF,UAAU,CAAC,qBAAqB,GAAG,IAAI,CAAA;QACzC,CAAC;QAED,MAAM,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC;YAC7C,IAAI,EAAE,aAAa;YACnB,UAAU;YACV,MAAM,EAAE,IAAI;YACZ,QAAQ;YACR,IAAI,EAAE,WAAW;YACjB,gBAAgB;YAChB,iBAAiB,EAAE,CAAC,IAAI,CAAC,UAAU;SACpC,CAAC,CAAA;IACJ,CAAC;IAES,2BAA2B;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAA;QACrC,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAA;QACpF,OAAO,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,OAAO,CAAC,OAAO,MAAM,CAAA;IACrF,CAAC;IAED,IAAY,gBAAgB;QAC1B,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,CAAA;IACvC,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,KAAK,CAAA;IACd,CAAC;IAEO,KAAK,CAAC,+BAA+B,CAAC,OAAgB,EAAE,QAAkB,EAAE,aAAqB,EAAE,YAAoB,EAAE,KAAwB;QACvJ,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,gBAAgB,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,eAAe,CAAC,CAAA;QACzF,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAQ,EAAC,gBAAgB,IAAI,IAAI,CAAC,IAAI,CAAC,2BAAgB,EAAE,eAAe,CAAC,EAAE,MAAM,CAAC,CAAA;QAEvG,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;YAC7B,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,4BAA4B,EAAE,EAAE,+CAA+C,CAAC,CAAA;YACnG,OAAO,MAAM,CAAA;QACf,CAAC;QAED,oEAAoE;QACpE,mEAAmE;QACnE,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,iBAAiB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,CAAC,CAAA;QACvH,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAA;QAC1C,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAA;QAChC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC,CAAA;QAC/F,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,GAAG,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAA;QAEnH,oDAAoD;QACpD,IAAI,IAAA,8BAAe,GAAE,EAAE,CAAC;YACtB,IAAI,CAAC;gBACH,MAAM,4BAAiB,CAAC,IAAI,CAAC,aAAa,EAAE,eAAe,CAAC,CAAA;YAC9D,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,kBAAG,CAAC,IAAI,CAAC,wBAAwB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;gBAEjD,MAAM,EAAE,GAAG,MAAM,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAA;gBAClC,MAAM,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAA;gBAChC,iFAAiF;gBACjF,IAAI,CAAC,GAAG,CAAC,CAAA;gBACT,OAAO,CAAC,CAAC,MAAM,IAAA,WAAM,EAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC;oBACrD,oCAAoC;oBACpC,6DAA6D;oBAC7D,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAA;gBACnE,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,IAAA,eAAQ,EAAC,aAAa,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,cAAc,EAAE,cAAc,EAAE,EAAE,CAAC,CAAA;QACtF,CAAC;QACD,MAAM,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,0BAA0B,CAAC,CAAA;QAEhE,OAAO,OAAO,CAAC,iBAAiB,CAAA;QAChC,mCAAmC;QACnC,OAAO,CAAC,oBAAoB,GAAG,eAAe,CAAA;QAC9C,OAAO,MAAM,CAAA;IACf,CAAC;IAEO,iBAAiB,CAAC,KAAK,GAAG,KAAK;QACrC,4DAA4D;QAC5D,yBAAyB;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,MAAM,CAAA;QAChD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAA;QACrC,MAAM,UAAU,GAAG;YACjB,SAAS,QAAQ,iBAAiB,OAAO,CAAC,WAAW,GAAG;YACxD,SAAS,QAAQ,oBAAoB,OAAO,CAAC,OAAO,GAAG;YACvD,SAAS,QAAQ,oBAAoB,OAAO,CAAC,SAAS,GAAG;YACzD,SAAS,QAAQ,qBAAqB,OAAO,CAAC,WAAW,GAAG;YAC5D,SAAS,QAAQ,iBAAiB,OAAO,CAAC,YAAY,GAAG;SAC1D,CAAA;QACD,IAAI,KAAK,EAAE,CAAC;YACV,UAAU,CAAC,CAAC,CAAC,GAAG,SAAS,QAAQ,oBAAoB,OAAO,CAAC,YAAY,GAAG,CAAA;YAC5E,UAAU,CAAC,CAAC,CAAC,GAAG,SAAS,QAAQ,iBAAiB,OAAO,CAAC,YAAY,GAAG,CAAA;QAC3E,CAAC;QACD,IAAA,kBAAG,EAAC,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC,eAAe,EAAE,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,QAAQ,qBAAqB,EAAE,GAAG,CAAC,CAAC,CAAA;QACnI,IAAA,kBAAG,EAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,QAAQ,iBAAiB,EAAE,GAAG,CAAC,CAAC,CAAA;QACxF,OAAO,UAAU,CAAA;IACnB,CAAC;IAES,gBAAgB,CAAC,QAAiB,EAAE,OAAgB;QAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAE5B,MAAM,gBAAgB,GAAG,IAAI,+BAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAE9E,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,CAAC,SAAS,GAAG,IAAI,CAAA;YAExB,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,EAAE,CAAC;gBACrC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAA;YACjC,CAAC;YAED,gBAAgB,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;gBAC9B,MAAM,mBAAmB,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,mBAAmB,EAAE,yBAAyB,CAAC,CAAA;gBAC9G,IAAI,mBAAmB,IAAI,IAAI,EAAE,CAAC;oBAChC,OAAO,CAAC,UAAU,GAAG,mBAAmB,CAAA;gBAC1C,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,EAAE,CAAC;gBACrC,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAA;YACtC,CAAC;YAED,gBAAgB,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;gBAC9B,MAAM,eAAe,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,eAAe,EAAE,qBAAqB,CAAC,CAAA;gBAClG,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;oBAC5B,OAAO,CAAC,eAAe,GAAG,IAAI,CAAA;oBAC9B,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAA;oBACpC,OAAO,CAAC,sBAAsB,GAAG,eAAe,CAAA;gBAClD,CAAC;YACH,CAAC,CAAC,CAAA;YAEF,gBAAgB,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;gBAC9B,MAAM,MAAM,GAAG,CAAC,MAAM,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,gBAAgB,EAAE,sBAAsB,CAAC,CAAC,IAAI,wDAAwD,CAAA;gBACzJ,OAAO,CAAC,4BAA4B,GAAG,MAAM,CAAA;gBAC7C,OAAO,CAAC,8BAA8B,GAAG,CAAC,MAAM,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,kBAAkB,EAAE,wBAAwB,CAAC,CAAC,IAAI,MAAM,CAAA;YACvI,CAAC,CAAC,CAAA;YAEF,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,EAAE,CAAC;gBACrC,OAAO,CAAC,qCAAqC,GAAG,IAAI,CAAA;YACtD,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAChC,OAAO,CAAC,0BAA0B,GAAG,IAAI,CAAA;QAC3C,CAAC;QAED,IAAI,OAAO,CAAC,yBAAyB,KAAK,IAAI,EAAE,CAAC;YAC/C,OAAO,CAAC,kCAAkC,GAAG,IAAI,CAAA;QACnD,CAAC;QAED,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAC7C,OAAO,CAAC,mCAAmC,GAAG,IAAI,CAAA;QACpD,CAAC;QAED,IAAI,OAAO,CAAC,kCAAkC,EAAE,CAAC;YAC/C,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,IAAI,wCAAyB,CAAC,2GAA2G,CAAC,CAAA;YAClJ,CAAC;YACD,OAAO,CAAC,kCAAkC,GAAG,IAAI,CAAA;QACnD,CAAC;QAED,IAAI,OAAO,CAAC,iCAAiC,EAAE,CAAC;YAC9C,OAAO,CAAC,iCAAiC,GAAG,IAAI,CAAA;QAClD,CAAC;QAED,MAAM,aAAa,GAAG,IAAA,yDAAmB,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;QAE5D,IAAI,aAAa,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC;YACvC,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC,YAAY,CAAA;QACpD,CAAC;QAED,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC,YAAY,CAAA;QAElD,IAAI,OAAO,CAAC,wBAAwB,EAAE,CAAC;YACrC,OAAO,CAAC,4BAA4B,GAAG,IAAI,CAAA;QAC7C,CAAC;QAED,gBAAgB,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;YAC9B,MAAM,eAAe,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,eAAe,EAAE,qBAAqB,CAAC,CAAA;YAClG,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;gBAC5B,iFAAiF;gBACjF,OAAO,CAAC,gBAAgB,GAAG,eAAe,CAAA;gBAC1C,OAAO,CAAC,UAAU,GAAG,eAAe,CAAA;YACtC,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,OAAO,CAAC,sBAAsB,GAAG,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,oBAAoB,IAAI,2BAA2B,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC,CAAA;QACnI,IAAI,aAAa,CAAC,uBAAuB,KAAK,mEAA6B,CAAC,KAAK,EAAE,CAAC;YAClF,OAAO,CAAC,8BAA8B,GAAG,IAAI,CAAA;QAC/C,CAAC;QACD,IAAI,aAAa,CAAC,uBAAuB,KAAK,mEAA6B,CAAC,MAAM,EAAE,CAAC;YACnF,OAAO,CAAC,yBAAyB,GAAG,IAAI,CAAA;QAC1C,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,yBAAyB,EAAE,CAAC;YAC7C,OAAO,CAAC,iCAAiC,GAAG,IAAI,CAAA;QAClD,CAAC;QAED,IAAI,OAAO,CAAC,uBAAuB,KAAK,IAAI,EAAE,CAAC;YAC7C,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAA;QACtC,CAAC;QAED,OAAO,gBAAgB,CAAC,UAAU,EAAE,CAAA;IACtC,CAAC;IAEO,qCAAqC,CAAC,OAAgB;QAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAA;QACrC,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;QACvC,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACxB,OAAO,CAAC,YAAY,GAAG,WAAW,CAAA;QACpC,CAAC;QAED,wFAAwF;QACxF,IAAI,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,eAAe,EAAE,CAAC;YACrD,OAAO,CAAC,oBAAoB,GAAG,OAAO,CAAC,eAAe,CAAA;QACxD,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,OAAO,CAAC,sBAAsB,GAAG,GAAG,OAAO,CAAC,mBAAmB,KAAK,oDAA6B,EAAE,CAAA;QACrG,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,wBAAwB,GAAG,GAAG,OAAO,CAAC,mBAAmB,KAAK,sDAA+B,EAAE,CAAA;QACzG,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,OAAO,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC;YAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;YAC5B,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,OAAO,CAAC,eAAe,GAAG,IAAI,CAAA;YAChC,CAAC;YAED,OAAO,CAAC,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;QAC5D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAgB,EAAE,QAAkB,EAAE,MAAc;QAChF,MAAM,IAAI,GAAkB,IAAI,CAAC,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;QAClF,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAA;QAClC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACxC,MAAM,KAAK,GAAQ,OAAO,CAAC,IAAqB,CAAC,CAAA;YACjD,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAA;YACxB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,EAAE,CAAC,CAAA;YACjC,CAAC;QACH,CAAC;QAED,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAsB,CAAC,CAAA;YAC9C,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC;oBACtB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,CAAA;gBAC7B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,EAAE,CAAC,CAAA;YACjC,CAAC;QACH,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEd,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;YACxC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,CAAA;QACtD,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAA,oBAAS,GAAE,CAAA;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CACvB,QAAQ,EACR,OAAO,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,EACtF,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAC3D,CAAA;QAED,sCAAsC;QACtC,2EAA2E;QAC3E,oEAAoE;QACpE,MAAM,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAA;QAC1D,IAAI;QAEJ,MAAM,IAAA,4BAAa,EAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE;YACzC,iKAAiK;YACjK,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE;YAC1C,GAAG,EAAE,2BAAgB;SACtB,CAAC,CAAA;IACJ,CAAC;IAEO,KAAK,CAAC,kCAAkC;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,MAAM,eAAe,GAAG,IAAI,yCAAmB,EAAE,CAAA;QACjD,MAAM,gBAAgB,GAAG,IAAI,2BAAgB,CAAC,OAAO,CAAC,CAAA;QAEtD,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,2BAAgB,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC,CAAA;QAE/E,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,2BAAgB,EAAE,SAAS,CAAC,CAAA;QACzD,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QACzC,eAAe,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,WAAW,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC,CAAA;QAEtI,IAAA,8BAAmB,EAAC,eAAe,EAAE,gBAAgB,CAAC,CAAA;QAEtD,MAAM,WAAW,GAAG,IAAI,+BAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAEzE,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAA;QACrE,WAAW,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;YACzB,eAAe,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,uBAAuB,EAAE,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,CAAA;QAC7G,CAAC,CAAC,CAAA;QAEF,WAAW,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;YACzB,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAA;YAC5E,MAAM,IAAI,GAAG,MAAM,IAAA,eAAU,EAAC,aAAa,CAAC,CAAA;YAC5C,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;gBACvC,eAAe,CAAC,YAAY,CAAC,UAAU,EAAE,aAAa,CAAC,CAAA;YACzD,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,WAAW,CAAC,OAAO,CAAC,IAAA,sCAA2B,EAAC,cAAc,EAAE,QAAQ,EAAE,eAAe,EAAE,gBAAgB,CAAC,CAAC,CAAA;QAE7G,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;gBAC/B,WAAW,CAAC,OAAO,CAAC,IAAA,sCAA2B,EAAC,sBAAsB,EAAE,QAAQ,EAAE,eAAe,EAAE,gBAAgB,CAAC,CAAC,CAAA;YACvH,CAAC;YAED,WAAW,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;gBACzB,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,eAAe,CAAC,CAAA;gBACvF,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;oBAC1B,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;oBAC9D,eAAe,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;gBACxC,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;QAC9B,OAAO,eAAe,CAAC,KAAK,EAAE,CAAA;IAChC,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,cAAsB,EAAE,WAAoB,EAAE,KAAwB;QACrG,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,MAAM,gBAAgB,GAAG,IAAI,2BAAgB,CAAC,OAAO,CAAC,CAAA;QAEtD,MAAM,eAAe,GAAG,IAAI,yCAAmB,EAAE,CAAA;QACjD,MAAM,WAAW,GAAG,IAAI,+BAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAEzE,IAAI,WAAW,EAAE,CAAC;YAChB,0FAA0F;YAC1F,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAA,gCAAkB,EAAC,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAA;QACvG,CAAC;QAED,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;QAE9B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,OAAO,eAAe,CAAC,KAAK,EAAE,GAAG,cAAc,CAAA;QACjD,CAAC;QAED,MAAM,2BAA2B,GAAG,IAAI,CAAC,8BAA8B,EAAE,CAAA;QACzE,IAAI,2BAA2B,IAAI,IAAI,IAAI,2BAA2B,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpF,KAAK,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC1C,MAAM,wBAAwB,CAAC,2BAA2B,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC,CAAA;YACzF,CAAC;QACH,CAAC;QAED,MAAM,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAA;QAClD,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,2BAAgB,EAAE,SAAS,CAAC,EAAE,qBAAqB,CAAC,CAAC,CAAA;YACjG,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,8BAA8B,GAAG,IAAI,yCAAmB,EAAE,CAAA;gBAChE,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;oBACpC,MAAM,UAAU,GAAG,IAAA,sBAAO,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,+BAAY,CAAC,CAAA;oBACtD,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;wBAC7B,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,IAAA,sCAAuB,EAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;wBAChH,IAAI,iBAAiB,GAAG,WAAW,CAAA;wBACnC,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;4BACvB,iBAAiB,GAAG,wBAAwB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAA;4BACvE,8BAA8B,CAAC,IAAI,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAA;wBACpE,CAAC;wBAED,MAAM,IAAI,GAAG,IAAI,iBAAiB,GAAG,CAAA;wBACrC,MAAM,WAAW,GAAG,cAAc,QAAQ,CAAC,OAAO,CAAC,WAAW,GAAG,CAAA;wBACjE,MAAM,OAAO,GAAG,sBAAsB,CAAA;wBACtC,8BAA8B,CAAC,WAAW,CAAC,eAAe,EAAE,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,IAAI,EAAE,KAAK,IAAI,IAAI,WAAW,IAAI,OAAO,EAAE,CAAC,CAAA;oBAC7J,CAAC;gBACH,CAAC;gBACD,eAAe,CAAC,KAAK,CAAC,0BAA0B,EAAE,8BAA8B,CAAC,CAAA;YACnF,CAAC;iBAAM,CAAC;gBACN,MAAM,gCAAgC,GAAG,IAAI,yCAAmB,EAAE,CAAA;gBAClE,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;oBACpC,KAAK,MAAM,GAAG,IAAI,IAAA,sBAAO,EAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;wBACpC,gCAAgC,CAAC,WAAW,CAAC,iBAAiB,EAAE,IAAI,IAAA,+BAAY,EAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG,CAAC,CAAA;oBACjH,CAAC;gBACH,CAAC;gBACD,eAAe,CAAC,KAAK,CAAC,4BAA4B,EAAE,gCAAgC,CAAC,CAAA;YACvF,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC,KAAK,EAAE,GAAG,cAAc,CAAA;IACjD,CAAC;CACF;AAhrBD,gCAgrBC;AAED,KAAK,UAAU,wBAAwB,CAAC,2BAA0C,EAAE,GAAW,EAAE,IAAU,EAAE,eAAoC;IAC/I,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,WAAW,CAAC,CAAA;IAChD,MAAM,OAAO,GAAG,MAAM,IAAA,eAAU,EAAC,YAAY,CAAC,CAAA;IAC9C,IAAI,OAAO,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;QAC9C,OAAM;IACR,CAAC;IAED,MAAM,WAAW,GAAG,GAAG,IAAI,CAAC,GAAG,cAAc,CAAA;IAC7C,MAAM,mBAAmB,GAAG,MAAM,IAAA,SAAI,EAAC,YAAY,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;QAClE,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;QACpC,CAAC;aAAM,CAAC;YACN,OAAO,2BAA2B,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAA;QAClE,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACrC,MAAM,KAAK,GAAG,IAAI,yCAAmB,EAAE,CAAA;QACvC,KAAK,MAAM,IAAI,IAAI,mBAAmB,EAAE,CAAC;YACvC,KAAK,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QAChF,CAAC;QACD,eAAe,CAAC,KAAK,CAAC,eAAe,mBAAI,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;IAC3D,CAAC;AACH,CAAC;AAED,KAAK,UAAU,aAAa,CAAC,OAAe;IAC1C,SAAS,MAAM,CAAC,aAAsB;QACpC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;gBACnC,IAAI,CAAC;oBACH,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;wBAC5C,IAAI,CAAC,aAAa,EAAE,CAAC;4BACnB,kBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,qFAAqF,CAAC,CAAA;wBACrG,CAAC;wBACD,OAAO,CAAC,KAAK,CAAC,CAAA;oBAChB,CAAC;yBAAM,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC;wBACtB,OAAO,CAAC,IAAI,CAAC,CAAA;oBACf,CAAC;yBAAM,CAAC;wBACN,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;oBACnC,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,MAAM,CAAC,KAAK,CAAC,CAAA;gBACf,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACf,IAAI,MAAM,EAAE,CAAC;gBACX,OAAO,IAAI,CAAA;YACb,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA;YACnF,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,MAAM,MAAM,CAAC,KAAK,CAAC,CAAA;AACrB,CAAC;AAED,KAAK,UAAU,qBAAqB,CAAC,IAAY;IAC/C,OAAO;QACL,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,CAAC,MAAM,IAAA,eAAI,EAAC,IAAI,CAAC,CAAC,CAAC,IAAI;QAC7B,MAAM,EAAE,MAAM,IAAA,eAAQ,EAAC,IAAI,CAAC;KAC7B,CAAA;AACH,CAAC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { <PERSON>, as<PERSON><PERSON>y, AsyncTaskManager, exec, executeAppBuilder, getPlatformIconFileName, InvalidConfigurationError, log, spawnAndWrite, use, getPath7za } from \"builder-util\"\nimport { CURRENT_APP_INSTALLER_FILE_NAME, CURRENT_APP_PACKAGE_FILE_NAME, PackageFileInfo, UUID } from \"builder-util-runtime\"\nimport { exists, statOrNull, walk } from \"builder-util/out/fs\"\nimport _debug from \"debug\"\nimport * as fs from \"fs\"\nimport { readFile, stat, unlink } from \"fs-extra\"\nimport * as path from \"path\"\nimport { getBinFromUrl } from \"../../binDownload\"\nimport { Target } from \"../../core\"\nimport { DesktopShortcutCreationPolicy, getEffectiveOptions } from \"../../options/CommonWindowsInstallerConfiguration\"\nimport { computeSafeArtifactNameIfNeeded, normalizeExt } from \"../../platformPackager\"\nimport { hashFile } from \"../../util/hash\"\nimport { isMacOsCatalina } from \"../../util/macosVersion\"\nimport { time } from \"../../util/timer\"\nimport { execWine } from \"../../wine\"\nimport { WinPackager } from \"../../winPackager\"\nimport { archive, ArchiveOptions } from \"../archive\"\nimport { appendBlockmap, configureDifferentialAwareArchiveOptions, createBlockmap, createNsisWebDifferentialUpdateInfo } from \"../differentialUpdateInfoBuilder\"\nimport { getWindowsInstallationAppPackageName, getWindowsInstallationDirName } from \"../targetUtil\"\nimport { Commands } from \"./Commands\"\nimport { Defines } from \"./Defines\"\nimport { addCustomMessageFileInclude, createAddLangsMacro, LangConfigurator } from \"./nsisLang\"\nimport { computeLicensePage } from \"./nsisLicense\"\nimport { NsisOptions, PortableOptions } from \"./nsisOptions\"\nimport { NsisScriptGenerator } from \"./nsisScriptGenerator\"\nimport { AppPackageHelper, nsisTemplatesDir, NSIS_PATH, UninstallerReader, NsisTargetOptions } from \"./nsisUtil\"\n\nconst debug = _debug(\"electron-builder:nsis\")\n\n// noinspection SpellCheckingInspection\nconst ELECTRON_BUILDER_NS_UUID = UUID.parse(\"50e065bc-3134-11e6-9bab-38c9862bdaf3\")\n\n// noinspection SpellCheckingInspection\nconst nsisResourcePathPromise = () => getBinFromUrl(\"nsis-resources\", \"3.4.1\", \"Dqd6g+2buwwvoG1Vyf6BHR1b+25QMmPcwZx40atOT57gH27rkjOei1L0JTldxZu4NFoEmW4kJgZ3DlSWVON3+Q==\")\n\nconst USE_NSIS_BUILT_IN_COMPRESSOR = false\n\nexport class NsisTarget extends Target {\n  readonly options: NsisOptions\n\n  /** @private */\n  readonly archs: Map<Arch, string> = new Map()\n  readonly isAsyncSupported = false\n\n  constructor(\n    readonly packager: WinPackager,\n    readonly outDir: string,\n    targetName: string,\n    protected readonly packageHelper: AppPackageHelper\n  ) {\n    super(targetName)\n\n    this.packageHelper.refCount++\n\n    this.options =\n      targetName === \"portable\"\n        ? Object.create(null)\n        : {\n            preCompressedFileExtensions: [\".avi\", \".mov\", \".m4v\", \".mp4\", \".m4p\", \".qt\", \".mkv\", \".webm\", \".vmdk\"],\n            ...this.packager.config.nsis,\n          }\n\n    if (targetName !== \"nsis\") {\n      Object.assign(this.options, (this.packager.config as any)[targetName === \"nsis-web\" ? \"nsisWeb\" : targetName])\n    }\n\n    const deps = packager.info.metadata.dependencies\n    if (deps != null && deps[\"electron-squirrel-startup\"] != null) {\n      log.warn('\"electron-squirrel-startup\" dependency is not required for NSIS')\n    }\n\n    NsisTargetOptions.resolve(this.options)\n  }\n\n  build(appOutDir: string, arch: Arch) {\n    this.archs.set(arch, appOutDir)\n    return Promise.resolve()\n  }\n\n  get isBuildDifferentialAware(): boolean {\n    return !this.isPortable && this.options.differentialPackage !== false\n  }\n\n  private getPreCompressedFileExtensions(): Array<string> | null {\n    const result = this.isWebInstaller ? null : this.options.preCompressedFileExtensions\n    return result == null ? null : asArray(result).map(it => (it.startsWith(\".\") ? it : `.${it}`))\n  }\n\n  /** @private */\n  async buildAppPackage(appOutDir: string, arch: Arch): Promise<PackageFileInfo> {\n    const options = this.options\n    const packager = this.packager\n\n    const isBuildDifferentialAware = this.isBuildDifferentialAware\n    const format = !isBuildDifferentialAware && options.useZip ? \"zip\" : \"7z\"\n    const archiveFile = path.join(this.outDir, `${packager.appInfo.sanitizedName}-${packager.appInfo.version}-${Arch[arch]}.nsis.${format}`)\n    const preCompressedFileExtensions = this.getPreCompressedFileExtensions()\n    const archiveOptions: ArchiveOptions = {\n      withoutDir: true,\n      compression: packager.compression,\n      excluded: preCompressedFileExtensions == null ? null : preCompressedFileExtensions.map(it => `*${it}`),\n    }\n\n    const timer = time(`nsis package, ${Arch[arch]}`)\n    await archive(format, archiveFile, appOutDir, isBuildDifferentialAware ? configureDifferentialAwareArchiveOptions(archiveOptions) : archiveOptions)\n    timer.end()\n\n    if (isBuildDifferentialAware && this.isWebInstaller) {\n      const data = await appendBlockmap(archiveFile)\n      return {\n        ...data,\n        path: archiveFile,\n      }\n    } else {\n      return await createPackageFileInfo(archiveFile)\n    }\n  }\n\n  protected get installerFilenamePattern(): string {\n    // tslint:disable:no-invalid-template-strings\n    return \"${productName} \" + (this.isPortable ? \"\" : \"Setup \") + \"${version}.${ext}\"\n  }\n\n  private get isPortable(): boolean {\n    return this.name === \"portable\"\n  }\n\n  async finishBuild(): Promise<any> {\n    try {\n      const { pattern } = this.packager.artifactPatternConfig(this.options, this.installerFilenamePattern)\n      const builds = new Set([this.archs])\n      if (pattern.includes(\"${arch}\") && this.archs.size > 1) {\n        ;[...this.archs].forEach(([arch, appOutDir]) => builds.add(new Map().set(arch, appOutDir)))\n      }\n      const doBuildArchs = builds.values()\n      for (const archs of doBuildArchs) {\n        await this.buildInstaller(archs)\n      }\n    } finally {\n      await this.packageHelper.finishBuild()\n    }\n  }\n\n  private async buildInstaller(archs: Map<Arch, string>): Promise<any> {\n    const primaryArch = archs.size === 1 ? archs.keys().next().value : null\n    const packager = this.packager\n    const appInfo = packager.appInfo\n    const options = this.options\n    const installerFilename = packager.expandArtifactNamePattern(\n      options,\n      \"exe\",\n      primaryArch,\n      this.installerFilenamePattern,\n      false,\n      this.packager.platformSpecificBuildOptions.defaultArch\n    )\n    const oneClick = options.oneClick !== false\n    const installerPath = path.join(this.outDir, installerFilename)\n\n    const logFields: any = {\n      target: this.name,\n      file: log.filePath(installerPath),\n      archs: Array.from(archs.keys())\n        .map(it => Arch[it])\n        .join(\", \"),\n    }\n    const isPerMachine = options.perMachine === true\n\n    if (!this.isPortable) {\n      logFields.oneClick = oneClick\n      logFields.perMachine = isPerMachine\n    }\n\n    await packager.info.callArtifactBuildStarted(\n      {\n        targetPresentableName: this.name,\n        file: installerPath,\n        arch: primaryArch,\n      },\n      logFields\n    )\n\n    const guid = options.guid || UUID.v5(appInfo.id, ELECTRON_BUILDER_NS_UUID)\n    const uninstallAppKey = guid.replace(/\\\\/g, \" - \")\n    const defines: Defines = {\n      APP_ID: appInfo.id,\n      APP_GUID: guid,\n      // Windows bug - entry in Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall cannot have \\ symbols (dir)\n      UNINSTALL_APP_KEY: uninstallAppKey,\n      PRODUCT_NAME: appInfo.productName,\n      PRODUCT_FILENAME: appInfo.productFilename,\n      APP_FILENAME: getWindowsInstallationDirName(appInfo, !oneClick || isPerMachine),\n      APP_DESCRIPTION: appInfo.description,\n      VERSION: appInfo.version,\n\n      PROJECT_DIR: packager.projectDir,\n      BUILD_RESOURCES_DIR: packager.info.buildResourcesDir,\n\n      APP_PACKAGE_NAME: getWindowsInstallationAppPackageName(appInfo.name),\n    }\n    if (options.customNsisBinary?.debugLogging) {\n      defines.ENABLE_LOGGING_ELECTRON_BUILDER = null\n    }\n    if (uninstallAppKey !== guid) {\n      defines.UNINSTALL_REGISTRY_KEY_2 = `Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Uninstall\\\\${guid}`\n    }\n\n    const commands: Commands = {\n      OutFile: `\"${installerPath}\"`,\n      VIProductVersion: appInfo.getVersionInWeirdWindowsForm(),\n      VIAddVersionKey: this.computeVersionKey(),\n      Unicode: this.isUnicodeEnabled,\n    }\n\n    const isPortable = this.isPortable\n    const iconPath = (isPortable ? null : await packager.getResource(options.installerIcon, \"installerIcon.ico\")) || (await packager.getIconPath())\n    if (iconPath != null) {\n      if (isPortable) {\n        commands.Icon = `\"${iconPath}\"`\n      } else {\n        defines.MUI_ICON = iconPath\n        defines.MUI_UNICON = iconPath\n      }\n    }\n\n    const packageFiles: { [arch: string]: PackageFileInfo } = {}\n    let estimatedSize = 0\n    if (this.isPortable && options.useZip) {\n      for (const [arch, dir] of archs.entries()) {\n        defines[arch === Arch.x64 ? \"APP_DIR_64\" : arch === Arch.arm64 ? \"APP_DIR_ARM64\" : \"APP_DIR_32\"] = dir\n      }\n    } else if (USE_NSIS_BUILT_IN_COMPRESSOR && archs.size === 1) {\n      defines.APP_BUILD_DIR = archs.get(archs.keys().next().value)\n    } else {\n      await BluebirdPromise.map(archs.keys(), async arch => {\n        const { fileInfo, unpackedSize } = await this.packageHelper.packArch(arch, this)\n        const file = fileInfo.path\n        const defineKey = arch === Arch.x64 ? \"APP_64\" : arch === Arch.arm64 ? \"APP_ARM64\" : \"APP_32\"\n        defines[defineKey] = file\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n        const defineNameKey = `${defineKey}_NAME` as \"APP_64_NAME\" | \"APP_ARM64_NAME\" | \"APP_32_NAME\"\n        defines[defineNameKey] = path.basename(file)\n        // nsis expect a hexadecimal string\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n        const defineHashKey = `${defineKey}_HASH` as \"APP_64_HASH\" | \"APP_ARM64_HASH\" | \"APP_32_HASH\"\n        defines[defineHashKey] = Buffer.from(fileInfo.sha512, \"base64\").toString(\"hex\").toUpperCase()\n        // NSIS accepts size in KiloBytes and supports only whole numbers\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n        const defineUnpackedSizeKey = `${defineKey}_UNPACKED_SIZE` as \"APP_64_UNPACKED_SIZE\" | \"APP_ARM64_UNPACKED_SIZE\" | \"APP_32_UNPACKED_SIZE\"\n        defines[defineUnpackedSizeKey] = Math.ceil(unpackedSize / 1024).toString()\n\n        if (this.isWebInstaller) {\n          await packager.dispatchArtifactCreated(file, this, arch)\n          packageFiles[Arch[arch]] = fileInfo\n        }\n        const path7za = await getPath7za()\n        const archiveInfo = (await exec(path7za, [\"l\", file])).trim()\n        // after adding blockmap data will be \"Warnings: 1\" in the end of output\n        const match = /(\\d+)\\s+\\d+\\s+\\d+\\s+files/.exec(archiveInfo)\n        if (match == null) {\n          log.warn({ output: archiveInfo }, \"cannot compute size of app package\")\n        } else {\n          estimatedSize += parseInt(match[1], 10)\n        }\n      })\n    }\n\n    this.configureDefinesForAllTypeOfInstaller(defines)\n    if (isPortable) {\n      const { unpackDirName, requestExecutionLevel, splashImage } = options as PortableOptions\n      defines.REQUEST_EXECUTION_LEVEL = requestExecutionLevel || \"user\"\n\n      // https://github.com/electron-userland/electron-builder/issues/5764\n      if (typeof unpackDirName === \"string\" || !unpackDirName) {\n        defines.UNPACK_DIR_NAME = unpackDirName || (await executeAppBuilder([\"ksuid\"]))\n      }\n\n      if (splashImage != null) {\n        defines.SPLASH_IMAGE = path.resolve(packager.projectDir, splashImage)\n      }\n    } else {\n      await this.configureDefines(oneClick, defines)\n    }\n\n    if (estimatedSize !== 0) {\n      // in kb\n      defines.ESTIMATED_SIZE = Math.round(estimatedSize / 1024)\n    }\n\n    if (packager.compression === \"store\") {\n      commands.SetCompress = \"off\"\n    } else {\n      // difference - 33.540 vs 33.601, only 61 KB (but zip is faster to decompress)\n      // do not use /SOLID - \"With solid compression, files are uncompressed to temporary file before they are copied to their final destination\",\n      // it is not good for portable installer (where built-in NSIS compression is used). http://forums.winamp.com/showpost.php?p=2982902&postcount=6\n      commands.SetCompressor = \"zlib\"\n      if (!this.isWebInstaller) {\n        defines.COMPRESS = \"auto\"\n      }\n    }\n\n    debug(defines)\n    debug(commands)\n\n    if (packager.packagerOptions.effectiveOptionComputed != null && (await packager.packagerOptions.effectiveOptionComputed([defines, commands]))) {\n      return\n    }\n\n    // prepare short-version variants of defines and commands, to make an uninstaller that doesn't differ much from the previous one\n    const definesUninstaller = { ...defines }\n    const commandsUninstaller = { ...commands }\n    if (appInfo.shortVersion != null) {\n      definesUninstaller.VERSION = appInfo.shortVersion\n      commandsUninstaller.VIProductVersion = appInfo.shortVersionWindows\n      commandsUninstaller.VIAddVersionKey = this.computeVersionKey(true)\n    }\n\n    const sharedHeader = await this.computeCommonInstallerScriptHeader()\n    const script = isPortable\n      ? await readFile(path.join(nsisTemplatesDir, \"portable.nsi\"), \"utf8\")\n      : await this.computeScriptAndSignUninstaller(definesUninstaller, commandsUninstaller, installerPath, sharedHeader, archs)\n\n    // copy outfile name into main options, as the computeScriptAndSignUninstaller function was kind enough to add important data to temporary defines.\n    defines.UNINSTALLER_OUT_FILE = definesUninstaller.UNINSTALLER_OUT_FILE\n\n    await this.executeMakensis(defines, commands, sharedHeader + (await this.computeFinalScript(script, true, archs)))\n    await Promise.all<any>([packager.sign(installerPath), defines.UNINSTALLER_OUT_FILE == null ? Promise.resolve() : unlink(defines.UNINSTALLER_OUT_FILE)])\n\n    const safeArtifactName = computeSafeArtifactNameIfNeeded(installerFilename, () => this.generateGitHubInstallerName())\n    let updateInfo: any\n    if (this.isWebInstaller) {\n      updateInfo = createNsisWebDifferentialUpdateInfo(installerPath, packageFiles)\n    } else if (this.isBuildDifferentialAware) {\n      updateInfo = await createBlockmap(installerPath, this, packager, safeArtifactName)\n    }\n\n    if (updateInfo != null && isPerMachine && (oneClick || options.packElevateHelper)) {\n      updateInfo.isAdminRightsRequired = true\n    }\n\n    await packager.info.callArtifactBuildCompleted({\n      file: installerPath,\n      updateInfo,\n      target: this,\n      packager,\n      arch: primaryArch,\n      safeArtifactName,\n      isWriteUpdateInfo: !this.isPortable,\n    })\n  }\n\n  protected generateGitHubInstallerName(): string {\n    const appInfo = this.packager.appInfo\n    const classifier = appInfo.name.toLowerCase() === appInfo.name ? \"setup-\" : \"Setup-\"\n    return `${appInfo.name}-${this.isPortable ? \"\" : classifier}${appInfo.version}.exe`\n  }\n\n  private get isUnicodeEnabled(): boolean {\n    return this.options.unicode !== false\n  }\n\n  get isWebInstaller(): boolean {\n    return false\n  }\n\n  private async computeScriptAndSignUninstaller(defines: Defines, commands: Commands, installerPath: string, sharedHeader: string, archs: Map<Arch, string>): Promise<string> {\n    const packager = this.packager\n    const customScriptPath = await packager.getResource(this.options.script, \"installer.nsi\")\n    const script = await readFile(customScriptPath || path.join(nsisTemplatesDir, \"installer.nsi\"), \"utf8\")\n\n    if (customScriptPath != null) {\n      log.info({ reason: \"custom NSIS script is used\" }, \"uninstaller is not signed by electron-builder\")\n      return script\n    }\n\n    // https://github.com/electron-userland/electron-builder/issues/2103\n    // it is more safe and reliable to write uninstaller to our out dir\n    const uninstallerPath = path.join(this.outDir, `__uninstaller-${this.name}-${this.packager.appInfo.sanitizedName}.exe`)\n    const isWin = process.platform === \"win32\"\n    defines.BUILD_UNINSTALLER = null\n    defines.UNINSTALLER_OUT_FILE = isWin ? uninstallerPath : path.win32.join(\"Z:\", uninstallerPath)\n    await this.executeMakensis(defines, commands, sharedHeader + (await this.computeFinalScript(script, false, archs)))\n\n    // http://forums.winamp.com/showthread.php?p=3078545\n    if (isMacOsCatalina()) {\n      try {\n        await UninstallerReader.exec(installerPath, uninstallerPath)\n      } catch (error: any) {\n        log.warn(`packager.vm is used: ${error.message}`)\n\n        const vm = await packager.vm.value\n        await vm.exec(installerPath, [])\n        // Parallels VM can exit after command execution, but NSIS continue to be running\n        let i = 0\n        while (!(await exists(uninstallerPath)) && i++ < 100) {\n          // noinspection JSUnusedLocalSymbols\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          await new Promise((resolve, _reject) => setTimeout(resolve, 300))\n        }\n      }\n    } else {\n      await execWine(installerPath, null, [], { env: { __COMPAT_LAYER: \"RunAsInvoker\" } })\n    }\n    await packager.sign(uninstallerPath, \"signing NSIS uninstaller\")\n\n    delete defines.BUILD_UNINSTALLER\n    // platform-specific path, not wine\n    defines.UNINSTALLER_OUT_FILE = uninstallerPath\n    return script\n  }\n\n  private computeVersionKey(short = false) {\n    // Error: invalid VIProductVersion format, should be X.X.X.X\n    // so, we must strip beta\n    const localeId = this.options.language || \"1033\"\n    const appInfo = this.packager.appInfo\n    const versionKey = [\n      `/LANG=${localeId} ProductName \"${appInfo.productName}\"`,\n      `/LANG=${localeId} ProductVersion \"${appInfo.version}\"`,\n      `/LANG=${localeId} LegalCopyright \"${appInfo.copyright}\"`,\n      `/LANG=${localeId} FileDescription \"${appInfo.description}\"`,\n      `/LANG=${localeId} FileVersion \"${appInfo.buildVersion}\"`,\n    ]\n    if (short) {\n      versionKey[1] = `/LANG=${localeId} ProductVersion \"${appInfo.shortVersion}\"`\n      versionKey[4] = `/LANG=${localeId} FileVersion \"${appInfo.shortVersion}\"`\n    }\n    use(this.packager.platformSpecificBuildOptions.legalTrademarks, it => versionKey.push(`/LANG=${localeId} LegalTrademarks \"${it}\"`))\n    use(appInfo.companyName, it => versionKey.push(`/LANG=${localeId} CompanyName \"${it}\"`))\n    return versionKey\n  }\n\n  protected configureDefines(oneClick: boolean, defines: Defines): Promise<any> {\n    const packager = this.packager\n    const options = this.options\n\n    const asyncTaskManager = new AsyncTaskManager(packager.info.cancellationToken)\n\n    if (oneClick) {\n      defines.ONE_CLICK = null\n\n      if (options.runAfterFinish !== false) {\n        defines.RUN_AFTER_FINISH = null\n      }\n\n      asyncTaskManager.add(async () => {\n        const installerHeaderIcon = await packager.getResource(options.installerHeaderIcon, \"installerHeaderIcon.ico\")\n        if (installerHeaderIcon != null) {\n          defines.HEADER_ICO = installerHeaderIcon\n        }\n      })\n    } else {\n      if (options.runAfterFinish === false) {\n        defines.HIDE_RUN_AFTER_FINISH = null\n      }\n\n      asyncTaskManager.add(async () => {\n        const installerHeader = await packager.getResource(options.installerHeader, \"installerHeader.bmp\")\n        if (installerHeader != null) {\n          defines.MUI_HEADERIMAGE = null\n          defines.MUI_HEADERIMAGE_RIGHT = null\n          defines.MUI_HEADERIMAGE_BITMAP = installerHeader\n        }\n      })\n\n      asyncTaskManager.add(async () => {\n        const bitmap = (await packager.getResource(options.installerSidebar, \"installerSidebar.bmp\")) || \"${NSISDIR}\\\\Contrib\\\\Graphics\\\\Wizard\\\\nsis3-metro.bmp\"\n        defines.MUI_WELCOMEFINISHPAGE_BITMAP = bitmap\n        defines.MUI_UNWELCOMEFINISHPAGE_BITMAP = (await packager.getResource(options.uninstallerSidebar, \"uninstallerSidebar.bmp\")) || bitmap\n      })\n\n      if (options.allowElevation !== false) {\n        defines.MULTIUSER_INSTALLMODE_ALLOW_ELEVATION = null\n      }\n    }\n\n    if (options.perMachine === true) {\n      defines.INSTALL_MODE_PER_ALL_USERS = null\n    }\n\n    if (options.selectPerMachineByDefault === true) {\n      defines.INSTALL_MODE_PER_ALL_USERS_DEFAULT = null\n    }\n\n    if (!oneClick || options.perMachine === true) {\n      defines.INSTALL_MODE_PER_ALL_USERS_REQUIRED = null\n    }\n\n    if (options.allowToChangeInstallationDirectory) {\n      if (oneClick) {\n        throw new InvalidConfigurationError(\"allowToChangeInstallationDirectory makes sense only for assisted installer (please set oneClick to false)\")\n      }\n      defines.allowToChangeInstallationDirectory = null\n    }\n\n    if (options.removeDefaultUninstallWelcomePage) {\n      defines.removeDefaultUninstallWelcomePage = null\n    }\n\n    const commonOptions = getEffectiveOptions(options, packager)\n\n    if (commonOptions.menuCategory != null) {\n      defines.MENU_FILENAME = commonOptions.menuCategory\n    }\n\n    defines.SHORTCUT_NAME = commonOptions.shortcutName\n\n    if (options.deleteAppDataOnUninstall) {\n      defines.DELETE_APP_DATA_ON_UNINSTALL = null\n    }\n\n    asyncTaskManager.add(async () => {\n      const uninstallerIcon = await packager.getResource(options.uninstallerIcon, \"uninstallerIcon.ico\")\n      if (uninstallerIcon != null) {\n        // we don't need to copy MUI_UNICON (defaults to app icon), so, we have 2 defines\n        defines.UNINSTALLER_ICON = uninstallerIcon\n        defines.MUI_UNICON = uninstallerIcon\n      }\n    })\n\n    defines.UNINSTALL_DISPLAY_NAME = packager.expandMacro(options.uninstallDisplayName || \"${productName} ${version}\", null, {}, false)\n    if (commonOptions.isCreateDesktopShortcut === DesktopShortcutCreationPolicy.NEVER) {\n      defines.DO_NOT_CREATE_DESKTOP_SHORTCUT = null\n    }\n    if (commonOptions.isCreateDesktopShortcut === DesktopShortcutCreationPolicy.ALWAYS) {\n      defines.RECREATE_DESKTOP_SHORTCUT = null\n    }\n    if (!commonOptions.isCreateStartMenuShortcut) {\n      defines.DO_NOT_CREATE_START_MENU_SHORTCUT = null\n    }\n\n    if (options.displayLanguageSelector === true) {\n      defines.DISPLAY_LANG_SELECTOR = null\n    }\n\n    return asyncTaskManager.awaitTasks()\n  }\n\n  private configureDefinesForAllTypeOfInstaller(defines: Defines): void {\n    const appInfo = this.packager.appInfo\n    const companyName = appInfo.companyName\n    if (companyName != null) {\n      defines.COMPANY_NAME = companyName\n    }\n\n    // electron uses product file name as app data, define it as well to remove on uninstall\n    if (defines.APP_FILENAME !== appInfo.productFilename) {\n      defines.APP_PRODUCT_FILENAME = appInfo.productFilename\n    }\n\n    if (this.isWebInstaller) {\n      defines.APP_PACKAGE_STORE_FILE = `${appInfo.updaterCacheDirName}\\\\${CURRENT_APP_PACKAGE_FILE_NAME}`\n    } else {\n      defines.APP_INSTALLER_STORE_FILE = `${appInfo.updaterCacheDirName}\\\\${CURRENT_APP_INSTALLER_FILE_NAME}`\n    }\n\n    if (!this.isWebInstaller && defines.APP_BUILD_DIR == null) {\n      const options = this.options\n      if (options.useZip) {\n        defines.ZIP_COMPRESSION = null\n      }\n\n      defines.COMPRESSION_METHOD = options.useZip ? \"zip\" : \"7z\"\n    }\n  }\n\n  private async executeMakensis(defines: Defines, commands: Commands, script: string): Promise<void> {\n    const args: Array<string> = this.options.warningsAsErrors === false ? [] : [\"-WX\"]\n    args.push(\"-INPUTCHARSET\", \"UTF8\")\n    for (const name of Object.keys(defines)) {\n      const value: any = defines[name as keyof Defines]\n      if (value == null) {\n        args.push(`-D${name}`)\n      } else {\n        args.push(`-D${name}=${value}`)\n      }\n    }\n\n    for (const name of Object.keys(commands)) {\n      const value = commands[name as keyof Commands]\n      if (Array.isArray(value)) {\n        for (const c of value) {\n          args.push(`-X${name} ${c}`)\n        }\n      } else {\n        args.push(`-X${name} ${value}`)\n      }\n    }\n\n    args.push(\"-\")\n\n    if (this.packager.debugLogger.isEnabled) {\n      this.packager.debugLogger.add(\"nsis.script\", script)\n    }\n\n    const nsisPath = await NSIS_PATH()\n    const command = path.join(\n      nsisPath,\n      process.platform === \"darwin\" ? \"mac\" : process.platform === \"win32\" ? \"Bin\" : \"linux\",\n      process.platform === \"win32\" ? \"makensis.exe\" : \"makensis\"\n    )\n\n    // if (process.platform === \"win32\") {\n    // fix for an issue caused by virus scanners, locking the file during write\n    // https://github.com/electron-userland/electron-builder/issues/5005\n    await ensureNotBusy(commands[\"OutFile\"].replace(/\"/g, \"\"))\n    // }\n\n    await spawnAndWrite(command, args, script, {\n      // we use NSIS_CONFIG_CONST_DATA_PATH=no to build makensis on Linux, but in any case it doesn't use stubs as MacOS/Windows version, so, we explicitly set NSISDIR\n      env: { ...process.env, NSISDIR: nsisPath },\n      cwd: nsisTemplatesDir,\n    })\n  }\n\n  private async computeCommonInstallerScriptHeader(): Promise<string> {\n    const packager = this.packager\n    const options = this.options\n    const scriptGenerator = new NsisScriptGenerator()\n    const langConfigurator = new LangConfigurator(options)\n\n    scriptGenerator.include(path.join(nsisTemplatesDir, \"include\", \"StdUtils.nsh\"))\n\n    const includeDir = path.join(nsisTemplatesDir, \"include\")\n    scriptGenerator.addIncludeDir(includeDir)\n    scriptGenerator.flags([\"updated\", \"force-run\", \"keep-shortcuts\", \"no-desktop-shortcut\", \"delete-app-data\", \"allusers\", \"currentuser\"])\n\n    createAddLangsMacro(scriptGenerator, langConfigurator)\n\n    const taskManager = new AsyncTaskManager(packager.info.cancellationToken)\n\n    const pluginArch = this.isUnicodeEnabled ? \"x86-unicode\" : \"x86-ansi\"\n    taskManager.add(async () => {\n      scriptGenerator.addPluginDir(pluginArch, path.join(await nsisResourcePathPromise(), \"plugins\", pluginArch))\n    })\n\n    taskManager.add(async () => {\n      const userPluginDir = path.join(packager.info.buildResourcesDir, pluginArch)\n      const stat = await statOrNull(userPluginDir)\n      if (stat != null && stat.isDirectory()) {\n        scriptGenerator.addPluginDir(pluginArch, userPluginDir)\n      }\n    })\n\n    taskManager.addTask(addCustomMessageFileInclude(\"messages.yml\", packager, scriptGenerator, langConfigurator))\n\n    if (!this.isPortable) {\n      if (options.oneClick === false) {\n        taskManager.addTask(addCustomMessageFileInclude(\"assistedMessages.yml\", packager, scriptGenerator, langConfigurator))\n      }\n\n      taskManager.add(async () => {\n        const customInclude = await packager.getResource(this.options.include, \"installer.nsh\")\n        if (customInclude != null) {\n          scriptGenerator.addIncludeDir(packager.info.buildResourcesDir)\n          scriptGenerator.include(customInclude)\n        }\n      })\n    }\n\n    await taskManager.awaitTasks()\n    return scriptGenerator.build()\n  }\n\n  private async computeFinalScript(originalScript: string, isInstaller: boolean, archs: Map<Arch, string>): Promise<string> {\n    const packager = this.packager\n    const options = this.options\n    const langConfigurator = new LangConfigurator(options)\n\n    const scriptGenerator = new NsisScriptGenerator()\n    const taskManager = new AsyncTaskManager(packager.info.cancellationToken)\n\n    if (isInstaller) {\n      // http://stackoverflow.com/questions/997456/nsis-license-file-based-on-language-selection\n      taskManager.add(() => computeLicensePage(packager, options, scriptGenerator, langConfigurator.langs))\n    }\n\n    await taskManager.awaitTasks()\n\n    if (this.isPortable) {\n      return scriptGenerator.build() + originalScript\n    }\n\n    const preCompressedFileExtensions = this.getPreCompressedFileExtensions()\n    if (preCompressedFileExtensions != null && preCompressedFileExtensions.length !== 0) {\n      for (const [arch, dir] of archs.entries()) {\n        await generateForPreCompressed(preCompressedFileExtensions, dir, arch, scriptGenerator)\n      }\n    }\n\n    const fileAssociations = packager.fileAssociations\n    if (fileAssociations.length !== 0) {\n      scriptGenerator.include(path.join(path.join(nsisTemplatesDir, \"include\"), \"FileAssociation.nsh\"))\n      if (isInstaller) {\n        const registerFileAssociationsScript = new NsisScriptGenerator()\n        for (const item of fileAssociations) {\n          const extensions = asArray(item.ext).map(normalizeExt)\n          for (const ext of extensions) {\n            const customIcon = await packager.getResource(getPlatformIconFileName(item.icon, false), `${extensions[0]}.ico`)\n            let installedIconPath = \"$appExe,0\"\n            if (customIcon != null) {\n              installedIconPath = `$INSTDIR\\\\resources\\\\${path.basename(customIcon)}`\n              registerFileAssociationsScript.file(installedIconPath, customIcon)\n            }\n\n            const icon = `\"${installedIconPath}\"`\n            const commandText = `\"Open with ${packager.appInfo.productName}\"`\n            const command = '\"$appExe $\\\\\"%1$\\\\\"\"'\n            registerFileAssociationsScript.insertMacro(\"APP_ASSOCIATE\", `\"${ext}\" \"${item.name || ext}\" \"${item.description || \"\"}\" ${icon} ${commandText} ${command}`)\n          }\n        }\n        scriptGenerator.macro(\"registerFileAssociations\", registerFileAssociationsScript)\n      } else {\n        const unregisterFileAssociationsScript = new NsisScriptGenerator()\n        for (const item of fileAssociations) {\n          for (const ext of asArray(item.ext)) {\n            unregisterFileAssociationsScript.insertMacro(\"APP_UNASSOCIATE\", `\"${normalizeExt(ext)}\" \"${item.name || ext}\"`)\n          }\n        }\n        scriptGenerator.macro(\"unregisterFileAssociations\", unregisterFileAssociationsScript)\n      }\n    }\n\n    return scriptGenerator.build() + originalScript\n  }\n}\n\nasync function generateForPreCompressed(preCompressedFileExtensions: Array<string>, dir: string, arch: Arch, scriptGenerator: NsisScriptGenerator): Promise<void> {\n  const resourcesDir = path.join(dir, \"resources\")\n  const dirInfo = await statOrNull(resourcesDir)\n  if (dirInfo == null || !dirInfo.isDirectory()) {\n    return\n  }\n\n  const nodeModules = `${path.sep}node_modules`\n  const preCompressedAssets = await walk(resourcesDir, (file, stat) => {\n    if (stat.isDirectory()) {\n      return !file.endsWith(nodeModules)\n    } else {\n      return preCompressedFileExtensions.some(it => file.endsWith(it))\n    }\n  })\n\n  if (preCompressedAssets.length !== 0) {\n    const macro = new NsisScriptGenerator()\n    for (const file of preCompressedAssets) {\n      macro.file(`$INSTDIR\\\\${path.relative(dir, file).replace(/\\//g, \"\\\\\")}`, file)\n    }\n    scriptGenerator.macro(`customFiles_${Arch[arch]}`, macro)\n  }\n}\n\nasync function ensureNotBusy(outFile: string): Promise<void> {\n  function isBusy(wasBusyBefore: boolean): Promise<boolean> {\n    return new Promise((resolve, reject) => {\n      fs.open(outFile, \"r+\", (error, fd) => {\n        try {\n          if (error != null && error.code === \"EBUSY\") {\n            if (!wasBusyBefore) {\n              log.info({}, \"output file is locked for writing (maybe by virus scanner) => waiting for unlock...\")\n            }\n            resolve(false)\n          } else if (fd == null) {\n            resolve(true)\n          } else {\n            fs.close(fd, () => resolve(true))\n          }\n        } catch (error: any) {\n          reject(error)\n        }\n      })\n    }).then(result => {\n      if (result) {\n        return true\n      } else {\n        return new Promise(resolve => setTimeout(resolve, 2000)).then(() => isBusy(true))\n      }\n    })\n  }\n\n  await isBusy(false)\n}\n\nasync function createPackageFileInfo(file: string): Promise<PackageFileInfo> {\n  return {\n    path: file,\n    size: (await stat(file)).size,\n    sha512: await hashFile(file),\n  }\n}\n"]}