#!/bin/bash

# 贪吃蛇游戏 - 自动打包脚本
# 使用方法: ./build.sh [选项]
# 选项:
#   --dev     开发模式运行
#   --test    测试模式运行
#   --pack    仅打包应用（不生成安装包）
#   --dmg     打包成DMG安装包
#   --clean   清理构建文件
#   --icons   生成图标文件

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        print_error "npm 未安装"
        exit 1
    fi
    
    # 检查node_modules
    if [ ! -d "node_modules" ]; then
        print_warning "依赖未安装，正在安装..."
        npm install
    fi
    
    print_success "依赖检查完成"
}

# 生成图标
generate_icons() {
    print_info "生成应用图标..."
    
    cd assets
    
    # 检查ImageMagick
    if ! command -v convert &> /dev/null; then
        print_warning "ImageMagick 未安装，跳过图标生成"
        print_info "请运行: brew install imagemagick"
        cd ..
        return
    fi
    
    # 检查源文件
    if [ ! -f "icon.svg" ]; then
        print_error "icon.svg 文件不存在"
        cd ..
        return
    fi
    
    # 执行图标生成脚本
    if [ -f "create-icons.sh" ]; then
        chmod +x create-icons.sh
        ./create-icons.sh
    else
        print_warning "图标生成脚本不存在，手动生成图标..."
        
        # 生成PNG图标
        convert icon.svg -resize 512x512 icon.png
        convert icon.svg -resize 256x256 <EMAIL>
        convert icon.svg -resize 128x128 <EMAIL>
        
        # 生成ICO图标
        convert icon.svg -resize 256x256 icon.ico
        
        # 生成ICNS图标
        mkdir -p icon.iconset
        convert icon.svg -resize 16x16 icon.iconset/icon_16x16.png
        convert icon.svg -resize 32x32 icon.iconset/<EMAIL>
        convert icon.svg -resize 32x32 icon.iconset/icon_32x32.png
        convert icon.svg -resize 64x64 icon.iconset/<EMAIL>
        convert icon.svg -resize 128x128 icon.iconset/icon_128x128.png
        convert icon.svg -resize 256x256 icon.iconset/<EMAIL>
        convert icon.svg -resize 256x256 icon.iconset/icon_256x256.png
        convert icon.svg -resize 512x512 icon.iconset/<EMAIL>
        convert icon.svg -resize 512x512 icon.iconset/icon_512x512.png
        convert icon.svg -resize 1024x1024 icon.iconset/<EMAIL>
        
        iconutil -c icns icon.iconset
        rm -rf icon.iconset
        
        print_success "图标生成完成"
    fi
    
    cd ..
}

# 清理构建文件
clean_build() {
    print_info "清理构建文件..."
    
    rm -rf dist/
    rm -rf build/
    
    print_success "清理完成"
}

# 开发模式运行
run_dev() {
    print_info "启动开发模式..."
    npm run dev
}

# 测试模式运行
run_test() {
    print_info "启动测试模式..."
    npm start
}

# 打包应用
pack_app() {
    print_info "打包应用程序..."
    npm run pack
    print_success "应用打包完成，输出目录: dist/"
}

# 打包DMG
build_dmg() {
    print_info "打包DMG安装包..."
    npm run build:dmg
    
    # 检查输出文件
    if [ -f dist/*.dmg ]; then
        DMG_FILE=$(ls dist/*.dmg | head -n 1)
        DMG_SIZE=$(du -h "$DMG_FILE" | cut -f1)
        print_success "DMG打包完成!"
        print_info "文件: $DMG_FILE"
        print_info "大小: $DMG_SIZE"
    else
        print_error "DMG打包失败"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "贪吃蛇游戏 - 自动打包脚本"
    echo ""
    echo "使用方法: ./build.sh [选项]"
    echo ""
    echo "选项:"
    echo "  --dev     开发模式运行"
    echo "  --test    测试模式运行"
    echo "  --pack    仅打包应用（不生成安装包）"
    echo "  --dmg     打包成DMG安装包"
    echo "  --clean   清理构建文件"
    echo "  --icons   生成图标文件"
    echo "  --help    显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  ./build.sh --dev          # 开发模式运行"
    echo "  ./build.sh --dmg          # 打包成DMG"
    echo "  ./build.sh --clean --dmg  # 清理后打包"
}

# 主函数
main() {
    print_info "贪吃蛇游戏 - 自动打包脚本"
    print_info "==============================="
    
    # 如果没有参数，显示帮助
    if [ $# -eq 0 ]; then
        show_help
        exit 0
    fi
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dev)
                check_dependencies
                run_dev
                exit 0
                ;;
            --test)
                check_dependencies
                run_test
                exit 0
                ;;
            --pack)
                check_dependencies
                pack_app
                exit 0
                ;;
            --dmg)
                check_dependencies
                build_dmg
                exit 0
                ;;
            --clean)
                clean_build
                shift
                ;;
            --icons)
                generate_icons
                exit 0
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
        shift
    done
}

# 运行主函数
main "$@"
